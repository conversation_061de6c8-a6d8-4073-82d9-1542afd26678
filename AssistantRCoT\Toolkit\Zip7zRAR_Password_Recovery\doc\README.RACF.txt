Cracking RACF DES hashes with JtR
=================================

1. Run racf2john on RACF database files.

E.g. $ ../run/racf2john racf_database > hashes

2. Run john on the output of racf2john.

E.g. $ ../run/john hashes


Other useful RACF utilities
===========================

* https://www.nigelpentland.co.uk/utilities/ (includes RACFSNOW, racfmask, and racfunmask)

* https://github.com/zedsec390/masking (unmasks pre-DES RACF masked password hashes)
