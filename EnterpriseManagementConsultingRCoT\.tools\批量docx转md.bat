@echo off
setlocal enabledelayedexpansion

set "input_dir=F:\Downloads\piliang md"
set "output_dir=F:\Downloads\piliang_md"

if not exist "%output_dir%" mkdir "%output_dir%"

for %%f in ("%input_dir%\*.docx") do (
    set "filename=%%~nf"
    pandoc "%%f" -o "%output_dir%\!filename!.md" --wrap=none --extract-media="%output_dir%"
)
for %%f in ("%input_dir%\*.doc") do (
    set "filename=%%~nf"
    pandoc "%%f" -o "%output_dir%\!filename!.md" --wrap=none --extract-media="%output_dir%"
)