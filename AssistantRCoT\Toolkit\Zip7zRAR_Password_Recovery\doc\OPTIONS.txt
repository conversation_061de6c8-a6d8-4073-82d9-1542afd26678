	<PERSON> the R<PERSON><PERSON>'s command line syntax.
	(Updated in/for the Jumbo patch by <PERSON><PERSON> and magnum)

When invoked with no command line arguments, "john" prints its usage
summary.

The supported command line arguments are password file names and
options.  Many of the supported options accept additional arguments.

You can list any number of password files right on the command line of
"john".  You do not have to specify any options.  If valid password
files are specified but no options are given, <PERSON> will go through
the default selection of cracking modes with their default settings.

Options may be specified along with password files or on their own,
although some require that password files be specified and some do not
support operation on password files.

All options are case sensitive, can be abbreviated as long as the
abbreviations are unambiguous, can be prefixed with two dashes
(GNU-style) or with one dash, and can use "=" or ":" to indicate an
argument (if supported for a given option).

The supported options are as follows, square brackets denote optional
arguments:

--single[=(SECTION[,SECTION2,....,SECTIONn]|:rule)] "single crack" mode

Enables the "single crack" mode, using rules from the configuration
file section [List.Rules:Single].  If --single=Single_2 then the rules
from [List.Rules:Single_2] section would be used.  A rule or set of rules
(including with pre-processor) can also be provided directly on the
command line.  See --rules for more details.

--single-seed=WORD[,WORD][,...]

Add static word(s) for all salts in single mode.  Adding e.g. "Rockyou"
will have the same effect as having that word in all gecos fields in the
whole input file (except the latter would eat memory).

--single-wordlist=FILE

Same as --single-seed except words are read from a wordlist file.

--wordlist[=FILE]		wordlist mode, read words from FILE,
--stdin				or from stdin
--pipe				much like stdin, but supports rules

These are used to enable the wordlist mode.  If FILE is not specified,
the one defined in john.conf will be used.

--dupe-suppression		suppress all duplicates from wordlist

Normally, consecutive duplicates are ignored when reading a wordlist file.
This switch enables full dupe suppression, using some memory and a little
extra start-up time.  This option implies preload regardless of file size,
see the --mem-file-size option.

--loopback[=FILE]		use (a) pot file as a wordlist

This mode implies --dupe-suppression.  This is a special variant of wordlist
mode.  Parse the file as a .pot file, so only the actual password part of
each line will be used.  If FILE is not specified, the active .pot file will
be used.  This is very effective for applying rules to further permute words
that has been successful, or without rules after a run using --salts in case
the cracks are successful on other salts than the ones that has already been
attacked.  Note that by default, some rules are applied too (see john.conf
"LoopbackRules").  To disable that for a run, just use --rules=none.

--encoding=NAME

Input data in a character encoding other than the default.  See also
doc/ENCODINGS.  --list=encodings gives a list of supported encodings.

--rules[=(SECTION[,..]|:rule[;..])]

Enables word mangling rules that are read from the specified section, which
defaults to [List.Rules:Wordlist] if not given.

A rule or set of rules (including with pre-processor) can also be provided
directly on the command line.  To do so, first letter following the = must be
: then the rest of the command argument is interpreted as the literal data
for the rule(s) itself.  Multiple rules can be provided.  Each rule should be
separated with a semicolon (;) character.  So --rules=:'$[a-z]$[0-9];u' will
use the 2 rule lines: $[a-z]$[0-9]  and u

When rules are entered on the command line, any time a semicolon is used IN
a rule, then it must be preceded by a \ character, to escape it.
So --rules::'$\;$[0-9]' is just one rule, while --rules=:'$\;$[0-9];Az"tst"'
is two rules, and --rules=:'$;$[0-9]' would 'try' to generate 2 rules, BUT
the first will be invalid since it is just the $ character.

--rules-stack=(SECTION[,..]|:rule[;..])

Stacked rules. Adds a second pass of rules, applied after normal processing.
Example: "--single --rules-stack=ShiftToggle" will run single mode's normal
rules fully through the first (no-op) rule in the toggle ruleset.  Only then
will the latter set move on to its second rule, the main rules are rewinded,
and so on.  In other modes, eg. "--wordlist --rules --rules-stack=best64",
the stacked rules (best64 in this case) will do a full pass before the
base rule moves to its second rule.  This option also works fine with ALL
modes that normally don't support rules at all, eg. "--mask=password?d?d?d
--rules-stack=KoreLogic" (although that will disable any GPU-side mask
accleration).  You can even use --wordlist --rules-stack=SomeRule without
using any base rule.  The difference is in how it iterates: Normal --rules
will run all words through the first rule before moving the second rule,
while --rules-stack will apply all rules to the first word before moving on
to the second word from the list.  Please note that when using (rule x rule)
you very quickly end up with huge numbers of rules:  Running the "all"
ruleset twice as "--rules=all --rules-stack=all" will produce a whopping
53 trillion rules.  This feature is best used with very carefully picked
rulesets that are made to work together.


--incremental[=MODE]		"incremental" mode [using section MODE]

Enables the "incremental" mode, using the specified configuration file
definition (section [Incremental:MODE]).  If MODE is omitted, the
default is "ASCII" for most hash types and "LM_ASCII" for LM hashes.

--external=MODE			external mode or word filter

Enables an external mode, using external functions defined in section
[List.External:MODE].

--stdout[=LENGTH]		just output candidate passwords

When used with a cracking mode, except for "single crack", makes John
output the candidate passwords it generates to stdout instead of
actually trying them against password hashes; no password files may
be specified when this option is used.  If a LENGTH is given, John
assumes that to be the significant password length and truncates
longer words (like a few formats, eg. DEScrypt, do). If you instead
say --max-length=LENGTH, longer candidates will instead be rejected.

--restore[=NAME]		restore an interrupted session

Continues an interrupted cracking session, reading state information
from the specified session file or from $JOHN/john.rec by default.

--session=NAME			give a new session the NAME

This option can only be used when starting a new cracking session and
its purpose is to give the new session a name (to which John will
append the ".rec" suffix to form the session file name).  This is
useful for running multiple instances of John in parallel or to be
able to later recover a session other than the last one you interrupt.
john.log file will also be named NAME.log (whatever 'NAME' is), so
that any logging of the session work will end up in this file.

--status[=NAME]			print status of a session [called NAME]

Prints status of an interrupted or running session.  Note that on a
Unix-like system, you can get a detached running session to update its
session file by sending a SIGHUP to the appropriate "john" process;
then use this option to read in and display the status.

--make-charset=FILE		make a charset, overwriting FILE

Generates a charset file based on character frequencies from
$JOHN/john.pot, for use with the "incremental" mode.  The entire
$JOHN/john.pot will be used for the charset generation by default.  You
may restrict the set of passwords used by specifying some password files
(in which case only the cracked passwords that correspond to those
password files will be used), "--format", or/and "--external" (with an
external mode that defines a filter() function).  Note that you can
combine this with the --pot=FILE option.

--show[=left]			show cracked passwords

Shows the cracked passwords for given password files (which you must
specify).  You can use this option while another instance of John is
cracking to see what John did so far; to get the most up to date
information, first send a SIGHUP to the appropriate "john" process.
If --show=left then all uncracked hashes are listed (in a John 'input'
file format way).  =left is just that literal string "=left".  When
using --show=left you normally need to supply the --format option too.

--show=formats			parse hashes and show meta-information in JSON
--show=types				or in old custom format

Shows the formats/types of input hashes contained in the input file.
See doc/SHOW_FORMATS.md

--show=invalid			show lines in the input file that fail valid()

Shows the input hash lines which fail the valid() function (ie. these
hashes have something wrong with them and would not be processed).
When using this mode, you will want to run against a solid file,
that is made up of just one type hash.  Also, the --format=xx flag
is not required, but if not used, then the first valid line of the
file will be used as the format type.

--test[=TIME]			run tests and benchmarks for TIME seconds each

Tests all of the compiled in hashing algorithms for proper operation and
benchmarks them.  The "--format" option can be used to restrict this to
a specific algorithm.  Using --test=0 will do a very quick self-test but
will not produce any speed figures.

--stress-test[=TIME]		continuous self-test

Perform self-tests just like with --test except it loops until failure or
until aborted by user.  This is mostly for debugging.

--skip-self-tests		skip self tests

Tells John to skip self tests.  Basic integrity checks will be done but test
vectors will not be verified.  This option is useful to run many small
attacks in a row against slow hashes.  Usually it is not needed.  It affects
--test option: --skip-self-tests and --test together perform only benchmarks.

--users=[-]LOGIN|UID[,..]	[do not] load this (these) user(s)

Allows you to select just a few accounts for cracking or for other
operations.  A dash before the list can be used to invert the check
(that is, load information for all the accounts that are not listed).

--groups=[-]GID[,..]		load users [not] of this (these) group(s)

Tells John to load (or to not load) information for accounts in the
specified group(s) only.

--shells=[-]SHELL[,..]		load users with[out] this (these) shell(s)

This option is useful to load accounts with a valid shell only or to
not load accounts with a bad shell.  You can omit the path before a
shell name, so "--shells=csh" will match both "/bin/csh" and
"/usr/bin/csh", while "--shells=/bin/csh" will only match "/bin/csh".

--salts=[-]N[:MAX]		load salts with[out] at least N passwords

This is a feature which allows to achieve better performance in some
special cases.  For example, you can crack only some salts using
"--salts=2" faster and then crack the rest using "--salts=-2".  Total
cracking time will be about the same, but you will likely get some
passwords cracked earlier.  If MAX is listed, then no hashes are
loaded where there are more than MAX salts.  This is so that if you
have run --salts=25 and then later can run --salts=10:24 and none of
the hashes that were already done from the --salts=25 will be re-done.

--costs=[-]Cn[:Mn][,...]	load salts with[out] cost value Cn [to Mn]
				for tunable cost parameters
				(comma separated values/ranges per param.)

The option --cost= can be used to crack only hashes with similar
tunable costs, to avoid a slowdown caused by expensive cost parameter
settings of some hashes if there are other hashes which are less
expensive to compute.
This feature allows you to focus on salts with lower cost values first.
For formats with different tunable costs, you can specify a comma
separated list of values/ranges.
  --cost=-2 (cost of first cost parameter must be smaller than 2)
  --cost=2,10:20 (cost of 1st parameter is >= 2, of 2nd between 10 and 20)
  --cost=0,10:20 (first cost parameter ignored, 2nd between 10 and 20)
  --cost=0,-3 (first cost parameter ignored, 2nd smaller than 3)
Few formats have more than one tunable cost parameter.  Many saltless hashes
and several salted hashes lack any tunable cost parameters.  For these, a
dummy cost of 1 is reported.
The --list=format-details and --list=format-all-details options will
list the tunable cost parameters supported by a given format.

--pot=NAME			pot filename to use

By default, John will use john.pot.  This override allows using a different
john.pot-like file (to start from, and to store any found password into).

--save-memory=LEVEL		enable memory saving, at LEVEL 1..3

You might need this option if you don't have enough memory or don't
want John to affect other processes too much or don't need it to load
and print login names along with cracked passwords.  Level 1 tells John
not to waste memory on login names; it is only supported when a cracking
mode other than "single crack" is explicitly requested.  It has no
negative performance impact - in fact, it sometimes speeds things up.
Please note that without the --save-memory=1 option (or higher), John
will waste some memory on potential login names even if the password
hash files don't happen to contain any login names.  (The complete lack
of login names isn't known to John when it starts parsing the files, so
it has to record the fact that each individual entry doesn't have a
login name unless you specify this option.)  Levels 2 and 3 reduce use
of performance optimizations involving large lookup tables, and thus
have a negative performance impact.  You should probably avoid using
them unless John doesn't work or gets into swap otherwise.

--node=MIN[-MAX]/TOTAL		this node's number range out of TOTAL count

This option is intended to allow for some trivial manually-configured
parallel and distributed processing.  For example, to split the workload
across two nodes (which could be machines, CPU cores, etc.), you'd
specify "--node=1/2" on one invocation of John and "--node=2/2" on the
other.  (If you do this on just one machine and with the same build of
John, you will also need to specify different "--session" names for the
two simultaneous invocations.)  The nodes are assumed to be same speed
(if this is not the case, one will get ahead of the other and is likely
to be done sooner, unless you're using a cracking mode and settings such
that the session is not expected to ever "complete" - which is fine.)
If your nodes are of very different speed, you may compensate for that
by allocating ranges of node numbers to individual invocations.  For
example, if you use OpenMP-enabled builds of John on two machines,
OpenMP is supported (with good scalability) for the hash type you're
cracking, and one of the machines has twice more of similar speed CPU
cores than the other, then you may use "--node=1-2/3" on the twice
bigger machine (let it be nodes 1 and 2 out of 3 nodes total) and
"--node=3/3" on the smaller one.

Efficiency of this approach to parallel processing, as currently
implemented, varies by cracking mode and its settings (efficiency is
higher for incremental mode and for wordlist mode with many rules, and
lower for other cracking modes and for wordlist mode without rules or
with few rules), hash type (efficiency is higher for slower to compute
hashes), salt count (efficiency is higher for higher salt counts), and
node count (efficiency is higher for lower node counts).  Scalability
may be limited.  The highest node count you can reasonably use varies by
cracking mode, its settings, hash type, and salt count.  With
incremental mode, efficiency in terms of c/s rate is nearly perfect
(there's essentially no overhead), but some nodes may currently receive
too little work - and this problem is exacerbated by high node counts
(such as 100 or more) and/or restrictive settings (such as MinLen and
MaxLen set to the same value or to a narrow range, and/or a charset file
with few characters being used).  With wordlist mode, for high
efficiency the rule count (after preprocessor expansion) needs to be
many times higher than node count, unless the p/s rate is low anyway
(due to slow hash type and/or high salt count).

Since there's no communication between the nodes, hashes successfully
cracked by one node continue being cracked by other nodes.  This is
mostly OK for saltless hash types or when there's just one salt (since
the same number of hash computations is to be made anyway - namely, only
one per candidate password tested), but it is a serious drawback when
many different salts are present and their number could potentially be
decreasing as some hashes get cracked.

--fork=N			fork N processes

This option is only available on Unix-like systems.  It is an easy way
to make use of multiple CPUs or CPU cores - you simply specify the
number of John processes that you'd like to run.  You may use "--fork"
as an alternative to OpenMP, for formats currently lacking OpenMP
support, or/and along with OpenMP (e.g., on a machine with 64 logical
CPUs you might choose to run with "--fork=8" for 8 processes and use
OpenMP to run 8 threads per process).

You may use "--fork" along with "--node" to use multiple machines while
also running multiple John processes per machine.  For example, to use
two similar 8-core machines you may run "--fork=8 --node=1-8/16" on one
of the machines and "--fork=8 --node=9-16/16" on the other.  For a more
complicated example, if you have an 8-core machine and a 64-core machine
with similar per-core performance, you could run an OpenMP-enabled build
on both of them and use "--node=1/9" (without "--fork") on the first
machine (8 threads in 1 process) and "--fork=8 --node=2-9/9" on the
64-core machine (8 threads in 8 processes, for 64 threads total on this
machine).  With the current implementation, the node numbers range
assigned to each John invocation must match the "--fork" process count.

When running with "--fork", multiple ".rec" files are created, which are
then read back by "--status" and "--restore" if you use those options.
Just like with other options, you must not specify "--fork" along with
"--status" or "--restore", because these read the main (unnumbered)
".rec" file first, which contains the right "--fork" option in it,
resulting in further (numbered) ".rec" files being read as appropriate.

Under the hood, "--fork" makes use of the same functionality that
"--node" does, so the same efficiency and scalability limitations apply.
Despite of those, "--fork" is often much more efficient than OpenMP -
especially for fast to compute hash types (such as LM hashes), where
OpenMP overhead is often unacceptable.

Similarly to "--node", there's almost no communication between the
processes with "--fork".  Hashes successfully cracked by one process
continue being cracked by other processes.  Just like with "--node",
this is mostly OK for saltless hash types or when there's just one salt,
but it is a serious drawback when many different salts are present and
their number could potentially be decreasing as some hashes get cracked.
To have the cracked hashes (and possibly salts) removed from all
processes, you may interrupt and restore the session once in a while.

--format=NAME			force hash type NAME

Allows you to override the hash type detection.  As of John the Ripper
version 1.8.0, valid "format names" are descrypt, bsdicrypt, md5crypt,
bcrypt, LM, AFS, tripcode, dummy, and crypt (and many more are added in
Jumbo).  You can use this option when you're starting a cracking session
or along with one of: "--test", "--show", "--make-charset".  Note that
John can't crack hashes of different types at the same time.  If you
happen to get a password file that uses more than one hash type, then
you have to invoke John once for each hash type and you need to use this
option to make John crack hashes of types other than the one it would
autodetect by default.  Mainly for test and list purposes (see --list=WHAT),
you can use group aliases "dynamic", "cpu", "gpu", "opencl" and "cuda"
as a format name, or use one wildcard, as in "--format=mysql-*",
"--format=raw*ng" or "--format=*office".  There's also a special way
to match any substring within the "algorithm name" (the string shown
within brackets, e.g. "[DES 128/128 AVX-16]" using @tag syntax as in
e.g. "--format=@DES" or "--format=@AVX".  Regardless of method, the
matching ignores case.

"--format=crypt" may or may not be supported in a given build of John.
In default builds of John, this support is currently only included on
Linux and Solaris.  When specified (and supported), this option makes
John use the system's crypt(3) or crypt_r(3) function.  This may be
needed to audit password hashes supported by the system, but not yet
supported by John's own optimized cryptographic routines.  Currently,
this is the case for glibc 2.7+ SHA-crypt hashes as used by recent
versions of Fedora and Ubuntu, and for SunMD5 hashes supported (but not
used by default) on recent versions of Solaris.  In fact, you do not
have to explicitly specify "--format=crypt" for hashes of these specific
types unless you have other hash types (those supported by John
natively) in the password file(s) as well (in which case another hash
type may get detected unless you specify this option).

When benchmarking "--format=crypt", it will default to benchmark DES.
The "--subformat=TYPE" can be added for benchmarking other types, given
they are supported by the system.  Currently supported TYPEs are
md5crypt, bcrypt, sha256crypt and sha512crypt.

"--format=crypt" is also a way to make John crack crypt(3) hashes of
different types at the same time, but doing so results in poor
performance and in unnecessarily poor results (in terms of passwords
cracked) for hashes of the "faster" types (as compared to the "slower"
ones loaded for cracking at the same time).  So you are advised to use
separate invocations of John, one per hash type.

--mem-file-size=SIZE		max. size of wordlist to preload into memory

For performance reasons, Jumbo preloads the wordlist file into memory instead
of reading line by line, when rules are in use.  The default max. size file
for this to happen is shown in --list=hidden-options.  A special value is
--mem-file-size=0. This will force loading to memory regardless of file size
(take care not to exceed your physical memory limits, or things will just run
much slower).  NOTE if --save-memory > 2, default preload will be disabled.

--field-separator-char=c	Use 'c' instead of ':' as field separator

This option allows for using an arbitrary character that doesn't interfere
with field contents.  A popular alternative is using the TAB character and
this has special parsing (write it, literally, as --field-separator-char=tab).
Other non-printable characters can be written in hex \xHH notation (the TAB
character can also be specified with --field-separator-char=\x09).  Note that
a side effect is that the pot file will get this field separator too.

--fix-state-delay=N		only determine the wordlist offset every N times

This is an optimization for wordlist mode which helps on some systems.  This
just limits the number of times that the ftell() call is performed.  The one
side effect is that if John is aborted and later restarted it may redo more
tests.  Thus, the use of this option is only sensible desirable for very fast
hash types.

--no-log			disable log file

This will turn off logging to file (default john.log) in case you are sure
you wont need it.  The log can sometimes become huge (such as when working
with many 'fast' rules on a fast format).  The older name --nolog is
deprecated but will continue to work until next release.

--log-stderr

Log to screen (stderr) instead of to file.

--bare-always-valid=C

Normally, dynamic mode will only load hashes with tags in the input file (for
example, $dynamic_0$368dc60082c8b47e697826b282b94e3d) or when a specific format
is requested (eg. --format=dynamic_0).  If C is 'Y' or 'y', then the dynamic
format will always treat bare hashes as valid.  You can permanently enable this
option by editing john.conf and setting "DynamicAlwaysUseBareHashes".

--crack-status			show status line on every crack

This will automagically emit a status line at every cracked password.  This is
mostly for testing.

--progress-every=N		emit a status line every N seconds

This will automagically emit a status line every N seconds.  This is mostly
for testing.

--mkpc=N			force min/max keys per crypt to N

This option is for certain kinds of testing.  There is a performance impact.
Many formats perform the crypts in batches of several (sometimes thousands or
even tens of thousands) candidates.  This option forces it down from the
format's default.  For most purposes, you would use 1.  One good example is for
studying which rules give most "hits": Without this options, you can't know for
sure which rule produced a successful guess when analyzing the log file.

--min-length=N			request a minimum candidate length in bytes
--max-length=N			request a maximum candidate length in bytes

These options limit the length of candidates that will be tried.  In wordlist
mode (after applying rules, if any), a word that is not within the requested
limits will be rejected (as opposed to truncated).  Most other modes also
honour these settings.  For example, you can fully exhaust a short keyspace
with incremental mode with e.g. --max-len=4 and then start a longer session
for the rest with --min-length=5, or use one core for lengths up to 8 and
other cores for exact lengths of 9, 10 and so on.
Note: With UTF-8 encoding and no internal codepage, non-ASCII characters will
use more than one byte.

--max-run-time=[-]N

Gracefully exit after N seconds.  If you resume it, it will run for another N
seconds and exit again.
If a negative value is specified,  John will run for N seconds after the last
crack, ie. each cracked password will reset the timer.

--max-candidates=[-]N

Gracefully exit after N candidates were tried.  If you resume it, it will run
for another set of N candidates and exit again.  Note that some formats will
process candidates in "batches" of a few, or many, candidates so the number
may well be overshot by some amount.
If a negative value is specified,  John will process N candidates after the
last crack, ie. each cracked password will reset the counter.

--keep-guessing			search for alternative plaintexts

Some formats has plaintext collisions.  That means some (or many) different
plaintexts will result in the same hash, so are equally valid.  It may be due
to a design flaw as in BFEgg, or due to limited hash size as in CRC-32 - or
artificially limited keyspace as in Office formats before 2007 (a good sized
key is truncated to 40-bit on purpose - thank you Microsoft).  When this option
is used a successful crack will not result in that hash being removed from
further cracking, so you can use it to find alternative plaintexts.

--reject-printable		reject printable binaries

This esoteric option can be used to filter out many bogus hashes.  It only
works well with not too small binary sizes.  Rejected hashes are printed (as
text) to stderr.  It may fail to work with some formats that e.g. reverse
steps.  A good example of that is it does not work with raw-md5 format, but
works fine with dynamic_0 (which is also raw MD5 but less optimized).  An
example of hash that would be rejected is 6d61676e756d474747476d61676e756d
which is hex of the ASCII string "magnumGGGGmagnum" and not very likely
a real hash.  You'd be surprised what you can find in some dumps.

--verbosity=N			change verbosity

Make John more verbose, or less verbose.  Default level is 3.  For example,
level 2 will mute the extra messages (device, work sizes etc) printed by
OpenCL formats and level 1 will mute printing of cracked passwords to screen.

--list=WHAT			list capabilities

This option can be used to gain information about what rules, modes etc are
available.  Use --list=help for a list of valid arguments.
If information about formats is requested, a --format=[NAME|CLASS] option
can be used in addition to --list=WHAT.
If an additional option --config=FILE

--list=externals          List all external modes/filters
--list=ext-modes          List external modes [has a generate()]
--list=ext-filters        List external filters, including modes with a filter
--list=ext-filters-only   List external filters [lacks a generate()]
--list=ext-hybrids        List external hybrid modes [has a new()]
--list=formats            List all the supported formats in the sequence they
                          are registered (this is also the sequence that will
                          be used to identify the first format which detects
                          a valid ciphertext (password hash) in the input file
                          if John is started without the --format=NAME option)
--list=format-details     List all the supported formats in the same sequence
                          as with --list=formats, but with additional details
                          (which might be more interesting for developers than
                          for regular users), machine-parsable.  Fields:
                          -format label
                           This is to be used as NAME in --format=NAME
                          -maximum supported password length in bytes
                           For performance reasons, the maximum length
                           supported by John the Ripper can be shorter than
                           the maximum length supported by the application
                           which uses this format
                          -minimum keys per crypt
                          -maximum keys per crypt
                          -format flags
                          -number of hard coded test cases for --test
                          -algorithm name
                          -format name
                          -benchmark comment
                          -benchmark length
                          -binary size
                          -salt size
                          -example ciphertext
                          (the individual columns are separated by [tab])
--list=format-all-details List all the supported formats and their attributes
                          in the same sequence as with --list=format-details,
                          but in a more descriptive form readable by humans
--list=format-methods[:WHICH]
                          List all methods which are NOT default for each
                          format.  If 'WHICH' is set, then this listing
                          will ONLY be formats which have the 'WHICH' format
                          overridden.
--list=format-tests       List all format specific tests
                          (format name, number, hash, password)
                          If a format uses encoding specific tests, you can
                          use --encoding=NAME as an additional option to see
                          these encoding specific tests.
                          If a ciphertext contains line feeds or the separator
                          character (horizontal tab), just the format name and
                          the test number are written.  If the password has
                          line feeds, it will not be written.
                          (A warning message is written to stderr in this case)
--list=subformats	  List all the built-in dynamic formats

--regen-lost-salts=type:hash_sz:mask
                          Finds passwords AND salts in a set of raw hashes.
                          This function has been changed, and now is 'generic'.
                          See the file Regen-Lost-Salts.txt for information.


	Additional utilities (compiled/installed along with John).

There are some related utilities in John's run directory.  (Depending on
platform, these may be symlinks to the main John program binary.)

	unshadow PASSWORD-FILE SHADOW-FILE

Combines the "passwd" and "shadow" files (when you already have access
to both) for use with John.  You might need this since if you only
used your shadow file, the "Full Name" or "GECOS" information wouldn't
be used by the "single crack" mode (thus reducing its efficiency) and
you wouldn't be able to use the "--groups" and "--shells" options and
to select by UID with "--users".  You probably also want to see all of
the passwd file fields with "--show".

You'll usually want to redirect the output of "unshadow" to a file
which you then pass to John.

	unafs DATABASE-FILE CELL-NAME

Gets password hashes out of the binary AFS database and produces
output usable by John (you should redirect the output to a file).

	unique OUTPUT-FILE

Removes duplicates from a wordlist (read from stdin) without changing
the order of entries.  You might want to use this with John's
"--stdout" option if you've got a lot of disk space to trade for the
reduced cracking time (on possibly trying some duplicates as they
might be produced with word mangling rules).
This program has been updated.  It is faster, it now can 'cut' the
lines (in a couple of ways), and can unique the files data, AND also
unique it against an existing file.


	Scripts.

There are also some related scripts supplied in John's run directory.
(Binary packages of John may choose to install these along with the
documentation instead.)

	relbench BENCHMARK-FILE-1 BENCHMARK-FILE-2

relbench is a Perl script to compare two "john --test" benchmark runs,
such as for different machines, "make" targets, C compilers,
optimization options, or/and versions of John the Ripper.  To use it,
redirect the output of each "john --test" run to a file, then run the
script on the two files.  Most values output by the script indicate
relative performance seen on the second benchmark run as compared to the
first one, with the value of 1.0 indicating no change, values higher
than 1.0 indicating speedup, and values lower than 1.0 indicating
slowdown.  Specifically, the script outputs the minimum, maximum,
median, and geometric mean for the speedup (or slowdown) seen across the
many individual benchmarks that "john --test" performs.  It also outputs
the median absolute deviation (relative to the median) and geometric
standard deviation (relative to the geometric mean).  Of these two, a
median absolute deviation of 0.0 would indicate that no deviation from
the median is prevalent, whereas a geometric standard deviation of 1.0
would indicate that all benchmarks were sped up or slowed down by the
exact same ratio or their speed remained unchanged.  In practice, these
values will tend to deviate from 0.0 and 1.0, respectively.

	mailer PASSWORD-FILE

A shell script to send mail to all users whose passwords got cracked.
This is not always a good idea, though, since lots of people do not
check their e-mail or ignore such messages, and the messages can be a
hint for crackers.

You should probably deploy proactive password strength checking, such as
with passwdqc, before you ask users to change their passwords - whether
using this script or otherwise.  And you should edit the message inside
the script before possibly using it.

	makechr

This is a script to (re-)generate the .chr files using passwords in
john.pot and applying all of the defined external mode filters.  To
speed it up on a sufficiently large computer (RAM and CPUs), "&" may be
added after the John invocation inside the script (to run all of them in
parallel).
