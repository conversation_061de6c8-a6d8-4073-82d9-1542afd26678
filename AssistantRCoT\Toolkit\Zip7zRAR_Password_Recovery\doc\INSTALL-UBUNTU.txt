Please, read INSTALL for general information about installing <PERSON> on your
system.  Distro maintainers, please also read README-DISTROS for how to
build CPU fall-back chains for any x86-64 CPU.

== Preamble

    Important: This document is for a local build in user's home dir, not a
    system-wide installation. In fact, most likely you do not need to install
    <PERSON> the Ripper system-wide.  Instead, after you compile the source code
    (see below), you may simply enter the "run" directory and invoke <PERSON> from
    there, e.g., ./john --list=build-options.

    Commands not explicitly prefixed by sudo should be run as a regular user
    (the one that will use JtR) rather than as root.

== How to install on Ubuntu (also works on Ubuntu for Windows)

    The steps listed below might also work unchanged on Debian and
    derivatives (untested).

=== Preconditions and Required stuff

    mkdir -p ~/src
    sudo apt-get -y install build-essential libssl-dev git zlib1g-dev

==== Recommended (extra formats and performance)

    sudo apt-get -y install yasm libgmp-dev libpcap-dev pkg-config libbz2-dev

==== If you have an NVIDIA GPU (OpenCL support)

    sudo apt-get -y install nvidia-opencl-dev

==== If you have an AMD GPU (OpenCL support)

    - If you have a recent GPU card (see [1]), the amdgpu-pro graphics stack
      should be used.

      - Ubuntu Xenial 16.04 LTS and above:
        To install it, follow the instructions at [2]. The fglrx driver is not
        supported in 16.04 (XServer 1.18 is not supported). Canonical and AMD
        decided to use the new hybrid driver stack for 16.04. OpenCL users
        should stay on a supported release until the hybrid stack is available.

      - Ubuntu up to and including 15.10:
        sudo apt-get -y install ocl-icd-opencl-dev opencl-headers fglrx-dev

==== If you want a CPU device for OpenCL (OpenCL support)

    sudo apt-get -y install ocl-icd-opencl-dev opencl-headers pocl-opencl-icd

==== Optional MPI support

    You probably do not need MPI, it's only for multi-machine clusters with
    shared network storage.  Merely having it compiled in (even if unused)
    may have security and reliability drawbacks.  Most users should read up
    on the --fork options instead, which gets compiled in automagically if
    your system supports it.  Having said all this, if you're sure you do
    need MPI support: Read README.mpi for instructions.

    Please also note that OpenMP (Open Multi-Processing) is not the same
    as Open MPI (Message Passing Interface).  You do get OpenMP if your
    system supports it.

==== Optional REXGEN support (experimental; additional cracking modes)

    NOTE this support is experimental and may not build at all until tweaked.
    The rexgen library is notorious for changing its API between versions,
    breaking stuff.  Leave this step out unless you really know that you want
    it and are capable of fixing issues.  Last known good version is 2.0.8.

    sudo apt-get -y install cmake bison flex libicu-dev
    cd ~/src
    git clone --recursive https://github.com/teeshop/rexgen.git
    cd rexgen
    ./install.sh

    Then add "--enable-rexgen" to ./configure when building john.

=== Clone latest bleeding-edge Jumbo and build

==== Clone GIT repo

    cd ~/src
    git clone git://github.com/magnumripper/JohnTheRipper -b bleeding-jumbo john

==== Build

    cd ~/src/john/src
    ./configure && make -s clean && make -sj4

==== Alternative: OpenMP fall-back build (recommended)

    cd ~/src/john/src
    ./configure --disable-openmp && make -s clean && make -sj4
    mv ../run/john ../run/john-non-omp
    ./configure CPPFLAGS='-DOMP_FALLBACK -DOMP_FALLBACK_BINARY="\"john-non-omp\""'
    make -s clean && make -sj4

=== Install TAB-completion

    sudo make shell-completion

=== Test your build

    cd ~/src/john/run
    ./john --test=0

=== Benchmark your build (this also performs self-tests)

    ./john --test



Foot notes:

[1] https://www.amd.com/en/support/kb/release-notes/rn-radpro-lin-16-40#faq-AMD-Product-Compatibility
[2] https://www.amd.com/en/support/kb/release-notes/AMDGPU-INSTALLATION
