97=proba1[32]
51=proba2[32*256+35]
44=proba2[32*256+38]
51=proba2[32*256+39]
40=proba2[32*256+48]
35=proba2[32*256+49]
37=proba2[32*256+50]
51=proba2[32*256+51]
44=proba2[32*256+52]
44=proba2[32*256+53]
33=proba2[32*256+54]
44=proba2[32*256+55]
44=proba2[32*256+56]
44=proba2[32*256+57]
44=proba2[32*256+66]
44=proba2[32*256+67]
51=proba2[32*256+68]
44=proba2[32*256+69]
40=proba2[32*256+71]
51=proba2[32*256+73]
44=proba2[32*256+77]
44=proba2[32*256+78]
40=proba2[32*256+80]
51=proba2[32*256+82]
44=proba2[32*256+84]
51=proba2[32*256+86]
51=proba2[32*256+87]
33=proba2[32*256+97]
33=proba2[32*256+98]
25=proba2[32*256+99]
32=proba2[32*256+100]
37=proba2[32*256+101]
44=proba2[32*256+102]
37=proba2[32*256+103]
37=proba2[32*256+104]
40=proba2[32*256+105]
40=proba2[32*256+106]
51=proba2[32*256+107]
32=proba2[32*256+108]
24=proba2[32*256+109]
37=proba2[32*256+110]
37=proba2[32*256+111]
27=proba2[32*256+112]
51=proba2[32*256+113]
35=proba2[32*256+114]
32=proba2[32*256+115]
33=proba2[32*256+116]
40=proba2[32*256+117]
51=proba2[32*256+118]
44=proba2[32*256+121]
51=proba2[32*256+122]
80=proba1[33]
15=proba2[33*256+33]
51=proba2[33*256+34]
51=proba2[33*256+35]
51=proba2[33*256+40]
51=proba2[33*256+41]
51=proba2[33*256+42]
51=proba2[33*256+43]
51=proba2[33*256+48]
31=proba2[33*256+49]
37=proba2[33*256+50]
37=proba2[33*256+51]
51=proba2[33*256+52]
51=proba2[33*256+54]
35=proba2[33*256+55]
51=proba2[33*256+56]
44=proba2[33*256+57]
51=proba2[33*256+59]
51=proba2[33*256+61]
37=proba2[33*256+63]
51=proba2[33*256+64]
40=proba2[33*256+70]
51=proba2[33*256+71]
51=proba2[33*256+76]
37=proba2[33*256+78]
51=proba2[33*256+80]
51=proba2[33*256+83]
51=proba2[33*256+84]
51=proba2[33*256+87]
51=proba2[33*256+95]
40=proba2[33*256+97]
37=proba2[33*256+98]
37=proba2[33*256+99]
44=proba2[33*256+100]
37=proba2[33*256+101]
40=proba2[33*256+102]
40=proba2[33*256+103]
44=proba2[33*256+105]
44=proba2[33*256+106]
51=proba2[33*256+107]
33=proba2[33*256+108]
37=proba2[33*256+109]
35=proba2[33*256+110]
35=proba2[33*256+111]
37=proba2[33*256+112]
51=proba2[33*256+113]
31=proba2[33*256+114]
31=proba2[33*256+115]
31=proba2[33*256+116]
51=proba2[33*256+119]
51=proba2[33*256+120]
51=proba2[33*256+121]
44=proba2[33*256+122]
118=proba1[34]
17=proba2[34*256+38]
23=proba2[34*256+39]
23=proba2[34*256+40]
23=proba2[34*256+48]
23=proba2[34*256+54]
23=proba2[34*256+97]
23=proba2[34*256+99]
23=proba2[34*256+100]
23=proba2[34*256+101]
23=proba2[34*256+119]
86=proba1[35]
42=proba2[35*256+33]
26=proba2[35*256+35]
42=proba2[35*256+36]
35=proba2[35*256+38]
42=proba2[35*256+48]
24=proba2[35*256+49]
35=proba2[35*256+50]
26=proba2[35*256+51]
42=proba2[35*256+52]
42=proba2[35*256+53]
35=proba2[35*256+54]
35=proba2[35*256+55]
24=proba2[35*256+57]
42=proba2[35*256+63]
35=proba2[35*256+65]
42=proba2[35*256+83]
42=proba2[35*256+87]
28=proba2[35*256+97]
35=proba2[35*256+98]
42=proba2[35*256+99]
42=proba2[35*256+100]
42=proba2[35*256+102]
35=proba2[35*256+105]
35=proba2[35*256+106]
31=proba2[35*256+109]
31=proba2[35*256+110]
42=proba2[35*256+111]
42=proba2[35*256+112]
31=proba2[35*256+114]
42=proba2[35*256+115]
42=proba2[35*256+116]
42=proba2[35*256+118]
42=proba2[35*256+119]
42=proba2[35*256+121]
42=proba2[35*256+123]
82=proba1[36]
45=proba2[36*256+33]
45=proba2[36*256+35]
26=proba2[36*256+36]
45=proba2[36*256+37]
45=proba2[36*256+41]
34=proba2[36*256+42]
45=proba2[36*256+43]
39=proba2[36*256+48]
39=proba2[36*256+49]
34=proba2[36*256+50]
39=proba2[36*256+54]
45=proba2[36*256+55]
34=proba2[36*256+56]
34=proba2[36*256+57]
45=proba2[36*256+61]
45=proba2[36*256+65]
45=proba2[36*256+66]
45=proba2[36*256+67]
45=proba2[36*256+69]
45=proba2[36*256+84]
45=proba2[36*256+90]
45=proba2[36*256+94]
34=proba2[36*256+97]
39=proba2[36*256+98]
45=proba2[36*256+99]
32=proba2[36*256+100]
34=proba2[36*256+101]
32=proba2[36*256+102]
39=proba2[36*256+103]
39=proba2[36*256+104]
39=proba2[36*256+106]
39=proba2[36*256+107]
32=proba2[36*256+108]
29=proba2[36*256+109]
39=proba2[36*256+110]
45=proba2[36*256+111]
29=proba2[36*256+112]
45=proba2[36*256+114]
32=proba2[36*256+115]
29=proba2[36*256+116]
45=proba2[36*256+117]
39=proba2[36*256+118]
45=proba2[36*256+119]
45=proba2[36*256+120]
45=proba2[36*256+121]
39=proba2[36*256+122]
97=proba1[37]
32=proba2[37*256+35]
25=proba2[37*256+36]
32=proba2[37*256+38]
32=proba2[37*256+42]
32=proba2[37*256+43]
32=proba2[37*256+44]
32=proba2[37*256+49]
32=proba2[37*256+52]
32=proba2[37*256+71]
32=proba2[37*256+78]
18=proba2[37*256+97]
32=proba2[37*256+98]
32=proba2[37*256+99]
32=proba2[37*256+100]
32=proba2[37*256+109]
32=proba2[37*256+112]
18=proba2[37*256+115]
32=proba2[37*256+117]
86=proba1[38]
47=proba2[38*256+32]
47=proba2[38*256+34]
33=proba2[38*256+38]
40=proba2[38*256+39]
47=proba2[38*256+40]
40=proba2[38*256+43]
47=proba2[38*256+44]
40=proba2[38*256+45]
36=proba2[38*256+49]
47=proba2[38*256+50]
47=proba2[38*256+51]
47=proba2[38*256+52]
47=proba2[38*256+54]
40=proba2[38*256+55]
40=proba2[38*256+57]
47=proba2[38*256+66]
47=proba2[38*256+67]
47=proba2[38*256+69]
40=proba2[38*256+70]
47=proba2[38*256+71]
36=proba2[38*256+74]
47=proba2[38*256+77]
40=proba2[38*256+83]
40=proba2[38*256+87]
47=proba2[38*256+89]
29=proba2[38*256+97]
36=proba2[38*256+98]
22=proba2[38*256+99]
36=proba2[38*256+100]
31=proba2[38*256+101]
40=proba2[38*256+102]
36=proba2[38*256+103]
47=proba2[38*256+105]
40=proba2[38*256+106]
40=proba2[38*256+107]
29=proba2[38*256+108]
29=proba2[38*256+109]
36=proba2[38*256+110]
33=proba2[38*256+111]
36=proba2[38*256+112]
31=proba2[38*256+114]
36=proba2[38*256+115]
47=proba2[38*256+116]
47=proba2[38*256+117]
40=proba2[38*256+118]
40=proba2[38*256+119]
47=proba2[38*256+121]
47=proba2[38*256+122]
47=proba2[38*256+123]
118=proba1[39]
30=proba2[39*256+38]
30=proba2[39*256+39]
37=proba2[39*256+40]
37=proba2[39*256+44]
37=proba2[39*256+51]
37=proba2[39*256+52]
37=proba2[39*256+57]
30=proba2[39*256+83]
37=proba2[39*256+95]
18=proba2[39*256+97]
30=proba2[39*256+98]
26=proba2[39*256+99]
23=proba2[39*256+101]
37=proba2[39*256+103]
30=proba2[39*256+111]
26=proba2[39*256+114]
17=proba2[39*256+115]
37=proba2[39*256+119]
37=proba2[39*256+122]
91=proba1[40]
36=proba2[40*256+36]
36=proba2[40*256+37]
36=proba2[40*256+40]
25=proba2[40*256+41]
29=proba2[40*256+42]
22=proba2[40*256+45]
36=proba2[40*256+49]
36=proba2[40*256+54]
36=proba2[40*256+69]
36=proba2[40*256+94]
29=proba2[40*256+95]
29=proba2[40*256+97]
36=proba2[40*256+99]
36=proba2[40*256+100]
29=proba2[40*256+103]
29=proba2[40*256+105]
29=proba2[40*256+110]
36=proba2[40*256+112]
36=proba2[40*256+114]
29=proba2[40*256+115]
29=proba2[40*256+116]
36=proba2[40*256+118]
36=proba2[40*256+121]
36=proba2[40*256+124]
118=proba1[41]
13=proba2[41*256+40]
27=proba2[41*256+41]
27=proba2[41*256+54]
27=proba2[41*256+61]
27=proba2[41*256+103]
27=proba2[41*256+107]
27=proba2[41*256+108]
20=proba2[41*256+111]
20=proba2[41*256+112]
27=proba2[41*256+114]
78=proba1[42]
44=proba2[42*256+36]
44=proba2[42*256+38]
51=proba2[42*256+40]
51=proba2[42*256+41]
15=proba2[42*256+42]
51=proba2[42*256+44]
44=proba2[42*256+45]
44=proba2[42*256+46]
44=proba2[42*256+48]
33=proba2[42*256+49]
40=proba2[42*256+50]
34=proba2[42*256+51]
40=proba2[42*256+52]
37=proba2[42*256+54]
37=proba2[42*256+55]
51=proba2[42*256+56]
34=proba2[42*256+57]
51=proba2[42*256+59]
51=proba2[42*256+61]
51=proba2[42*256+63]
51=proba2[42*256+64]
51=proba2[42*256+65]
44=proba2[42*256+66]
51=proba2[42*256+67]
44=proba2[42*256+68]
51=proba2[42*256+69]
51=proba2[42*256+73]
51=proba2[42*256+77]
51=proba2[42*256+78]
51=proba2[42*256+80]
51=proba2[42*256+82]
51=proba2[42*256+95]
28=proba2[42*256+97]
40=proba2[42*256+98]
31=proba2[42*256+99]
40=proba2[42*256+100]
51=proba2[42*256+101]
51=proba2[42*256+102]
37=proba2[42*256+103]
37=proba2[42*256+105]
44=proba2[42*256+106]
37=proba2[42*256+108]
33=proba2[42*256+109]
44=proba2[42*256+110]
51=proba2[42*256+111]
37=proba2[42*256+112]
44=proba2[42*256+113]
51=proba2[42*256+114]
33=proba2[42*256+115]
51=proba2[42*256+116]
51=proba2[42*256+117]
37=proba2[42*256+118]
51=proba2[42*256+119]
51=proba2[42*256+120]
51=proba2[42*256+122]
96=proba1[43]
38=proba2[43*256+38]
19=proba2[43*256+43]
31=proba2[43*256+45]
29=proba2[43*256+49]
38=proba2[43*256+50]
45=proba2[43*256+51]
45=proba2[43*256+52]
31=proba2[43*256+53]
29=proba2[43*256+54]
34=proba2[43*256+55]
38=proba2[43*256+61]
45=proba2[43*256+66]
38=proba2[43*256+67]
45=proba2[43*256+72]
38=proba2[43*256+74]
38=proba2[43*256+75]
45=proba2[43*256+76]
45=proba2[43*256+80]
45=proba2[43*256+85]
45=proba2[43*256+89]
38=proba2[43*256+97]
29=proba2[43*256+98]
38=proba2[43*256+99]
45=proba2[43*256+101]
38=proba2[43*256+102]
45=proba2[43*256+103]
45=proba2[43*256+104]
45=proba2[43*256+105]
45=proba2[43*256+106]
45=proba2[43*256+107]
29=proba2[43*256+108]
38=proba2[43*256+109]
45=proba2[43*256+110]
38=proba2[43*256+111]
45=proba2[43*256+112]
38=proba2[43*256+113]
45=proba2[43*256+115]
31=proba2[43*256+116]
38=proba2[43*256+119]
38=proba2[43*256+120]
96=proba1[44]
24=proba2[44*256+44]
28=proba2[44*256+46]
44=proba2[44*256+48]
28=proba2[44*256+49]
33=proba2[44*256+50]
37=proba2[44*256+51]
33=proba2[44*256+55]
33=proba2[44*256+56]
37=proba2[44*256+57]
28=proba2[44*256+59]
37=proba2[44*256+65]
44=proba2[44*256+66]
44=proba2[44*256+73]
44=proba2[44*256+83]
44=proba2[44*256+84]
44=proba2[44*256+86]
37=proba2[44*256+97]
44=proba2[44*256+98]
37=proba2[44*256+99]
33=proba2[44*256+100]
37=proba2[44*256+102]
44=proba2[44*256+103]
44=proba2[44*256+105]
37=proba2[44*256+106]
44=proba2[44*256+107]
23=proba2[44*256+109]
37=proba2[44*256+110]
30=proba2[44*256+112]
37=proba2[44*256+115]
26=proba2[44*256+116]
37=proba2[44*256+118]
44=proba2[44*256+121]
44=proba2[44*256+122]
91=proba1[45]
57=proba2[45*256+34]
57=proba2[45*256+36]
43=proba2[45*256+38]
57=proba2[45*256+39]
57=proba2[45*256+40]
43=proba2[45*256+43]
27=proba2[45*256+45]
33=proba2[45*256+48]
23=proba2[45*256+49]
32=proba2[45*256+50]
43=proba2[45*256+51]
43=proba2[45*256+52]
46=proba2[45*256+53]
33=proba2[45*256+54]
36=proba2[45*256+55]
36=proba2[45*256+56]
30=proba2[45*256+57]
57=proba2[45*256+63]
57=proba2[45*256+65]
50=proba2[45*256+66]
50=proba2[45*256+67]
50=proba2[45*256+68]
57=proba2[45*256+69]
50=proba2[45*256+70]
50=proba2[45*256+71]
57=proba2[45*256+73]
57=proba2[45*256+74]
57=proba2[45*256+75]
50=proba2[45*256+76]
57=proba2[45*256+77]
57=proba2[45*256+78]
57=proba2[45*256+80]
50=proba2[45*256+84]
57=proba2[45*256+87]
46=proba2[45*256+88]
57=proba2[45*256+97]
39=proba2[45*256+98]
33=proba2[45*256+99]
41=proba2[45*256+100]
50=proba2[45*256+101]
35=proba2[45*256+102]
36=proba2[45*256+103]
41=proba2[45*256+104]
39=proba2[45*256+105]
36=proba2[45*256+106]
50=proba2[45*256+107]
30=proba2[45*256+108]
35=proba2[45*256+109]
37=proba2[45*256+110]
43=proba2[45*256+111]
39=proba2[45*256+112]
35=proba2[45*256+114]
36=proba2[45*256+115]
32=proba2[45*256+116]
57=proba2[45*256+117]
46=proba2[45*256+118]
41=proba2[45*256+119]
50=proba2[45*256+120]
57=proba2[45*256+121]
57=proba2[45*256+122]
93=proba1[46]
54=proba2[46*256+32]
54=proba2[46*256+35]
54=proba2[46*256+43]
41=proba2[46*256+44]
23=proba2[46*256+46]
24=proba2[46*256+48]
27=proba2[46*256+49]
32=proba2[46*256+50]
34=proba2[46*256+51]
41=proba2[46*256+52]
35=proba2[46*256+53]
41=proba2[46*256+54]
36=proba2[46*256+55]
29=proba2[46*256+56]
38=proba2[46*256+57]
43=proba2[46*256+65]
47=proba2[46*256+70]
54=proba2[46*256+71]
47=proba2[46*256+73]
54=proba2[46*256+74]
47=proba2[46*256+78]
54=proba2[46*256+79]
54=proba2[46*256+83]
41=proba2[46*256+84]
54=proba2[46*256+85]
54=proba2[46*256+87]
38=proba2[46*256+97]
35=proba2[46*256+98]
36=proba2[46*256+99]
35=proba2[46*256+100]
41=proba2[46*256+101]
38=proba2[46*256+102]
43=proba2[46*256+103]
41=proba2[46*256+104]
54=proba2[46*256+105]
38=proba2[46*256+106]
54=proba2[46*256+107]
34=proba2[46*256+108]
38=proba2[46*256+109]
34=proba2[46*256+110]
47=proba2[46*256+111]
47=proba2[46*256+112]
54=proba2[46*256+113]
54=proba2[46*256+114]
30=proba2[46*256+115]
38=proba2[46*256+117]
47=proba2[46*256+118]
43=proba2[46*256+119]
47=proba2[46*256+120]
54=proba2[46*256+121]
47=proba2[46*256+122]
46=proba1[48]
94=proba2[48*256+32]
83=proba2[48*256+33]
94=proba2[48*256+35]
78=proba2[48*256+36]
87=proba2[48*256+37]
87=proba2[48*256+41]
94=proba2[48*256+42]
83=proba2[48*256+43]
76=proba2[48*256+45]
73=proba2[48*256+46]
14=proba2[48*256+48]
20=proba2[48*256+49]
26=proba2[48*256+50]
27=proba2[48*256+51]
27=proba2[48*256+52]
27=proba2[48*256+53]
27=proba2[48*256+54]
22=proba2[48*256+55]
27=proba2[48*256+56]
29=proba2[48*256+57]
87=proba2[48*256+59]
94=proba2[48*256+60]
94=proba2[48*256+64]
75=proba2[48*256+65]
78=proba2[48*256+66]
87=proba2[48*256+67]
94=proba2[48*256+69]
83=proba2[48*256+70]
87=proba2[48*256+71]
87=proba2[48*256+72]
87=proba2[48*256+73]
94=proba2[48*256+74]
80=proba2[48*256+75]
80=proba2[48*256+76]
72=proba2[48*256+77]
83=proba2[48*256+78]
94=proba2[48*256+79]
94=proba2[48*256+80]
83=proba2[48*256+82]
76=proba2[48*256+83]
87=proba2[48*256+84]
83=proba2[48*256+86]
94=proba2[48*256+88]
80=proba2[48*256+90]
94=proba2[48*256+95]
53=proba2[48*256+97]
56=proba2[48*256+98]
52=proba2[48*256+99]
56=proba2[48*256+100]
60=proba2[48*256+101]
59=proba2[48*256+102]
63=proba2[48*256+103]
67=proba2[48*256+104]
69=proba2[48*256+105]
63=proba2[48*256+106]
63=proba2[48*256+107]
54=proba2[48*256+108]
51=proba2[48*256+109]
55=proba2[48*256+110]
62=proba2[48*256+111]
58=proba2[48*256+112]
78=proba2[48*256+113]
56=proba2[48*256+114]
54=proba2[48*256+115]
56=proba2[48*256+116]
60=proba2[48*256+117]
64=proba2[48*256+118]
65=proba2[48*256+119]
65=proba2[48*256+120]
72=proba2[48*256+121]
66=proba2[48*256+122]
38=proba1[49]
75=proba2[49*256+33]
95=proba2[49*256+35]
88=proba2[49*256+36]
84=proba2[49*256+38]
95=proba2[49*256+41]
75=proba2[49*256+42]
82=proba2[49*256+43]
79=proba2[49*256+44]
73=proba2[49*256+45]
69=proba2[49*256+46]
21=proba2[49*256+48]
21=proba2[49*256+49]
17=proba2[49*256+50]
26=proba2[49*256+51]
28=proba2[49*256+52]
28=proba2[49*256+53]
30=proba2[49*256+54]
27=proba2[49*256+55]
29=proba2[49*256+56]
20=proba2[49*256+57]
88=proba2[49*256+59]
84=proba2[49*256+61]
84=proba2[49*256+64]
67=proba2[49*256+65]
76=proba2[49*256+66]
76=proba2[49*256+67]
76=proba2[49*256+68]
82=proba2[49*256+69]
84=proba2[49*256+70]
79=proba2[49*256+71]
88=proba2[49*256+72]
84=proba2[49*256+73]
88=proba2[49*256+74]
82=proba2[49*256+75]
75=proba2[49*256+76]
72=proba2[49*256+77]
76=proba2[49*256+78]
79=proba2[49*256+79]
82=proba2[49*256+80]
82=proba2[49*256+82]
79=proba2[49*256+83]
75=proba2[49*256+84]
95=proba2[49*256+85]
88=proba2[49*256+86]
88=proba2[49*256+87]
95=proba2[49*256+88]
95=proba2[49*256+89]
84=proba2[49*256+90]
46=proba2[49*256+97]
50=proba2[49*256+98]
52=proba2[49*256+99]
54=proba2[49*256+100]
54=proba2[49*256+101]
58=proba2[49*256+102]
56=proba2[49*256+103]
64=proba2[49*256+104]
59=proba2[49*256+105]
56=proba2[49*256+106]
63=proba2[49*256+107]
55=proba2[49*256+108]
51=proba2[49*256+109]
53=proba2[49*256+110]
59=proba2[49*256+111]
56=proba2[49*256+112]
63=proba2[49*256+113]
56=proba2[49*256+114]
51=proba2[49*256+115]
54=proba2[49*256+116]
67=proba2[49*256+117]
60=proba2[49*256+118]
63=proba2[49*256+119]
65=proba2[49*256+120]
70=proba2[49*256+121]
69=proba2[49*256+122]
42=proba1[50]
82=proba2[50*256+32]
69=proba2[50*256+33]
82=proba2[50*256+35]
86=proba2[50*256+36]
86=proba2[50*256+37]
86=proba2[50*256+39]
71=proba2[50*256+42]
82=proba2[50*256+43]
93=proba2[50*256+44]
71=proba2[50*256+45]
65=proba2[50*256+46]
20=proba2[50*256+48]
22=proba2[50*256+49]
23=proba2[50*256+50]
18=proba2[50*256+51]
26=proba2[50*256+52]
25=proba2[50*256+53]
28=proba2[50*256+54]
26=proba2[50*256+55]
28=proba2[50*256+56]
30=proba2[50*256+57]
86=proba2[50*256+59]
86=proba2[50*256+61]
86=proba2[50*256+63]
82=proba2[50*256+64]
69=proba2[50*256+65]
69=proba2[50*256+66]
65=proba2[50*256+67]
77=proba2[50*256+68]
86=proba2[50*256+69]
79=proba2[50*256+70]
73=proba2[50*256+71]
75=proba2[50*256+72]
86=proba2[50*256+73]
82=proba2[50*256+74]
79=proba2[50*256+75]
82=proba2[50*256+76]
72=proba2[50*256+77]
71=proba2[50*256+78]
93=proba2[50*256+79]
75=proba2[50*256+80]
86=proba2[50*256+81]
75=proba2[50*256+82]
79=proba2[50*256+83]
71=proba2[50*256+84]
86=proba2[50*256+85]
79=proba2[50*256+86]
93=proba2[50*256+87]
86=proba2[50*256+88]
75=proba2[50*256+89]
73=proba2[50*256+90]
93=proba2[50*256+91]
93=proba2[50*256+95]
46=proba2[50*256+97]
45=proba2[50*256+98]
46=proba2[50*256+99]
49=proba2[50*256+100]
53=proba2[50*256+101]
50=proba2[50*256+102]
56=proba2[50*256+103]
57=proba2[50*256+104]
56=proba2[50*256+105]
56=proba2[50*256+106]
59=proba2[50*256+107]
51=proba2[50*256+108]
46=proba2[50*256+109]
56=proba2[50*256+110]
59=proba2[50*256+111]
50=proba2[50*256+112]
61=proba2[50*256+113]
48=proba2[50*256+114]
49=proba2[50*256+115]
53=proba2[50*256+116]
60=proba2[50*256+117]
57=proba2[50*256+118]
55=proba2[50*256+119]
63=proba2[50*256+120]
59=proba2[50*256+121]
58=proba2[50*256+122]
52=proba1[51]
69=proba2[51*256+33]
88=proba2[51*256+34]
77=proba2[51*256+35]
88=proba2[51*256+36]
88=proba2[51*256+37]
82=proba2[51*256+38]
71=proba2[51*256+42]
82=proba2[51*256+43]
88=proba2[51*256+44]
68=proba2[51*256+45]
60=proba2[51*256+46]
21=proba2[51*256+48]
22=proba2[51*256+49]
24=proba2[51*256+50]
23=proba2[51*256+51]
20=proba2[51*256+52]
26=proba2[51*256+53]
28=proba2[51*256+54]
28=proba2[51*256+55]
29=proba2[51*256+56]
32=proba2[51*256+57]
88=proba2[51*256+59]
88=proba2[51*256+61]
88=proba2[51*256+62]
77=proba2[51*256+63]
82=proba2[51*256+64]
61=proba2[51*256+65]
64=proba2[51*256+66]
68=proba2[51*256+67]
68=proba2[51*256+68]
72=proba2[51*256+69]
71=proba2[51*256+70]
64=proba2[51*256+71]
88=proba2[51*256+72]
82=proba2[51*256+73]
77=proba2[51*256+74]
69=proba2[51*256+75]
71=proba2[51*256+76]
68=proba2[51*256+77]
77=proba2[51*256+78]
88=proba2[51*256+79]
72=proba2[51*256+80]
88=proba2[51*256+81]
66=proba2[51*256+82]
77=proba2[51*256+83]
71=proba2[51*256+84]
88=proba2[51*256+85]
75=proba2[51*256+86]
88=proba2[51*256+87]
77=proba2[51*256+88]
69=proba2[51*256+90]
77=proba2[51*256+95]
43=proba2[51*256+97]
48=proba2[51*256+98]
45=proba2[51*256+99]
42=proba2[51*256+100]
48=proba2[51*256+101]
52=proba2[51*256+102]
50=proba2[51*256+103]
54=proba2[51*256+104]
55=proba2[51*256+105]
51=proba2[51*256+106]
57=proba2[51*256+107]
51=proba2[51*256+108]
44=proba2[51*256+109]
53=proba2[51*256+110]
54=proba2[51*256+111]
48=proba2[51*256+112]
61=proba2[51*256+113]
47=proba2[51*256+114]
49=proba2[51*256+115]
52=proba2[51*256+116]
60=proba2[51*256+117]
53=proba2[51*256+118]
58=proba2[51*256+119]
61=proba2[51*256+120]
61=proba2[51*256+121]
54=proba2[51*256+122]
56=proba1[52]
80=proba2[52*256+32]
87=proba2[52*256+33]
87=proba2[52*256+35]
80=proba2[52*256+36]
73=proba2[52*256+37]
87=proba2[52*256+39]
87=proba2[52*256+40]
87=proba2[52*256+42]
68=proba2[52*256+43]
87=proba2[52*256+44]
76=proba2[52*256+45]
69=proba2[52*256+46]
22=proba2[52*256+48]
23=proba2[52*256+49]
25=proba2[52*256+50]
29=proba2[52*256+51]
24=proba2[52*256+52]
19=proba2[52*256+53]
27=proba2[52*256+54]
25=proba2[52*256+55]
28=proba2[52*256+56]
30=proba2[52*256+57]
80=proba2[52*256+59]
87=proba2[52*256+61]
87=proba2[52*256+64]
69=proba2[52*256+65]
80=proba2[52*256+66]
69=proba2[52*256+67]
80=proba2[52*256+68]
80=proba2[52*256+69]
73=proba2[52*256+70]
71=proba2[52*256+71]
80=proba2[52*256+72]
87=proba2[52*256+73]
73=proba2[52*256+74]
80=proba2[52*256+75]
76=proba2[52*256+76]
73=proba2[52*256+77]
76=proba2[52*256+78]
87=proba2[52*256+79]
66=proba2[52*256+80]
87=proba2[52*256+81]
68=proba2[52*256+82]
87=proba2[52*256+83]
73=proba2[52*256+84]
76=proba2[52*256+85]
80=proba2[52*256+86]
76=proba2[52*256+87]
87=proba2[52*256+88]
76=proba2[52*256+89]
76=proba2[52*256+90]
80=proba2[52*256+95]
42=proba2[52*256+97]
47=proba2[52*256+98]
50=proba2[52*256+99]
48=proba2[52*256+100]
46=proba2[52*256+101]
48=proba2[52*256+102]
50=proba2[52*256+103]
53=proba2[52*256+104]
54=proba2[52*256+105]
52=proba2[52*256+106]
57=proba2[52*256+107]
49=proba2[52*256+108]
43=proba2[52*256+109]
52=proba2[52*256+110]
62=proba2[52*256+111]
49=proba2[52*256+112]
60=proba2[52*256+113]
51=proba2[52*256+114]
47=proba2[52*256+115]
48=proba2[52*256+116]
51=proba2[52*256+117]
57=proba2[52*256+118]
57=proba2[52*256+119]
58=proba2[52*256+120]
54=proba2[52*256+121]
57=proba2[52*256+122]
57=proba1[53]
80=proba2[53*256+32]
80=proba2[53*256+33]
76=proba2[53*256+35]
76=proba2[53*256+36]
80=proba2[53*256+37]
87=proba2[53*256+38]
87=proba2[53*256+39]
69=proba2[53*256+43]
87=proba2[53*256+44]
71=proba2[53*256+45]
71=proba2[53*256+46]
21=proba2[53*256+48]
23=proba2[53*256+49]
26=proba2[53*256+50]
28=proba2[53*256+51]
26=proba2[53*256+52]
24=proba2[53*256+53]
19=proba2[53*256+54]
24=proba2[53*256+55]
27=proba2[53*256+56]
26=proba2[53*256+57]
87=proba2[53*256+59]
87=proba2[53*256+61]
80=proba2[53*256+63]
68=proba2[53*256+65]
68=proba2[53*256+66]
64=proba2[53*256+67]
87=proba2[53*256+68]
87=proba2[53*256+69]
67=proba2[53*256+70]
68=proba2[53*256+71]
80=proba2[53*256+72]
80=proba2[53*256+73]
73=proba2[53*256+74]
87=proba2[53*256+75]
73=proba2[53*256+76]
73=proba2[53*256+77]
80=proba2[53*256+78]
80=proba2[53*256+79]
68=proba2[53*256+80]
87=proba2[53*256+81]
73=proba2[53*256+82]
71=proba2[53*256+83]
71=proba2[53*256+84]
76=proba2[53*256+85]
87=proba2[53*256+86]
76=proba2[53*256+87]
87=proba2[53*256+88]
80=proba2[53*256+89]
87=proba2[53*256+90]
45=proba2[53*256+97]
48=proba2[53*256+98]
49=proba2[53*256+99]
52=proba2[53*256+100]
50=proba2[53*256+101]
50=proba2[53*256+102]
50=proba2[53*256+103]
58=proba2[53*256+104]
56=proba2[53*256+105]
56=proba2[53*256+106]
55=proba2[53*256+107]
52=proba2[53*256+108]
46=proba2[53*256+109]
54=proba2[53*256+110]
58=proba2[53*256+111]
49=proba2[53*256+112]
57=proba2[53*256+113]
51=proba2[53*256+114]
49=proba2[53*256+115]
51=proba2[53*256+116]
62=proba2[53*256+117]
58=proba2[53*256+118]
60=proba2[53*256+119]
62=proba2[53*256+120]
54=proba2[53*256+121]
57=proba2[53*256+122]
56=proba1[54]
75=proba2[54*256+32]
70=proba2[54*256+33]
81=proba2[54*256+36]
81=proba2[54*256+42]
81=proba2[54*256+43]
68=proba2[54*256+45]
70=proba2[54*256+46]
23=proba2[54*256+48]
26=proba2[54*256+49]
30=proba2[54*256+50]
29=proba2[54*256+51]
26=proba2[54*256+52]
27=proba2[54*256+53]
15=proba2[54*256+54]
24=proba2[54*256+55]
26=proba2[54*256+56]
22=proba2[54*256+57]
88=proba2[54*256+59]
81=proba2[54*256+61]
88=proba2[54*256+63]
88=proba2[54*256+64]
68=proba2[54*256+65]
61=proba2[54*256+66]
81=proba2[54*256+67]
77=proba2[54*256+68]
77=proba2[54*256+69]
75=proba2[54*256+70]
77=proba2[54*256+71]
77=proba2[54*256+72]
66=proba2[54*256+73]
88=proba2[54*256+74]
68=proba2[54*256+75]
88=proba2[54*256+76]
77=proba2[54*256+77]
88=proba2[54*256+78]
88=proba2[54*256+79]
75=proba2[54*256+80]
81=proba2[54*256+81]
75=proba2[54*256+82]
77=proba2[54*256+83]
88=proba2[54*256+84]
88=proba2[54*256+85]
77=proba2[54*256+86]
75=proba2[54*256+87]
88=proba2[54*256+88]
88=proba2[54*256+89]
88=proba2[54*256+90]
88=proba2[54*256+93]
88=proba2[54*256+94]
47=proba2[54*256+97]
49=proba2[54*256+98]
52=proba2[54*256+99]
51=proba2[54*256+100]
53=proba2[54*256+101]
53=proba2[54*256+102]
55=proba2[54*256+103]
58=proba2[54*256+104]
55=proba2[54*256+105]
54=proba2[54*256+106]
54=proba2[54*256+107]
53=proba2[54*256+108]
48=proba2[54*256+109]
53=proba2[54*256+110]
59=proba2[54*256+111]
50=proba2[54*256+112]
64=proba2[54*256+113]
53=proba2[54*256+114]
47=proba2[54*256+115]
54=proba2[54*256+116]
64=proba2[54*256+117]
57=proba2[54*256+118]
65=proba2[54*256+119]
61=proba2[54*256+120]
58=proba2[54*256+121]
61=proba2[54*256+122]
55=proba1[55]
82=proba2[55*256+32]
71=proba2[55*256+33]
78=proba2[55*256+35]
78=proba2[55*256+36]
82=proba2[55*256+38]
71=proba2[55*256+42]
82=proba2[55*256+43]
82=proba2[55*256+44]
67=proba2[55*256+45]
67=proba2[55*256+46]
23=proba2[55*256+48]
24=proba2[55*256+49]
26=proba2[55*256+50]
26=proba2[55*256+51]
24=proba2[55*256+52]
23=proba2[55*256+53]
25=proba2[55*256+54]
20=proba2[55*256+55]
23=proba2[55*256+56]
26=proba2[55*256+57]
78=proba2[55*256+59]
82=proba2[55*256+64]
71=proba2[55*256+65]
71=proba2[55*256+66]
73=proba2[55*256+67]
71=proba2[55*256+68]
75=proba2[55*256+69]
75=proba2[55*256+70]
89=proba2[55*256+71]
78=proba2[55*256+72]
75=proba2[55*256+73]
78=proba2[55*256+74]
78=proba2[55*256+75]
71=proba2[55*256+76]
71=proba2[55*256+77]
75=proba2[55*256+78]
82=proba2[55*256+79]
78=proba2[55*256+80]
89=proba2[55*256+81]
78=proba2[55*256+82]
78=proba2[55*256+83]
78=proba2[55*256+84]
89=proba2[55*256+85]
75=proba2[55*256+86]
75=proba2[55*256+87]
78=proba2[55*256+88]
82=proba2[55*256+89]
82=proba2[55*256+90]
48=proba2[55*256+97]
50=proba2[55*256+98]
51=proba2[55*256+99]
53=proba2[55*256+100]
54=proba2[55*256+101]
56=proba2[55*256+102]
52=proba2[55*256+103]
60=proba2[55*256+104]
55=proba2[55*256+105]
53=proba2[55*256+106]
56=proba2[55*256+107]
50=proba2[55*256+108]
49=proba2[55*256+109]
55=proba2[55*256+110]
61=proba2[55*256+111]
53=proba2[55*256+112]
67=proba2[55*256+113]
58=proba2[55*256+114]
52=proba2[55*256+115]
53=proba2[55*256+116]
56=proba2[55*256+117]
54=proba2[55*256+118]
63=proba2[55*256+119]
62=proba2[55*256+120]
61=proba2[55*256+121]
58=proba2[55*256+122]
89=proba2[55*256+124]
60=proba1[56]
86=proba2[56*256+32]
75=proba2[56*256+33]
75=proba2[56*256+36]
86=proba2[56*256+38]
86=proba2[56*256+41]
66=proba2[56*256+42]
86=proba2[56*256+44]
68=proba2[56*256+45]
66=proba2[56*256+46]
19=proba2[56*256+48]
21=proba2[56*256+49]
24=proba2[56*256+50]
25=proba2[56*256+51]
27=proba2[56*256+52]
27=proba2[56*256+53]
27=proba2[56*256+54]
25=proba2[56*256+55]
25=proba2[56*256+56]
27=proba2[56*256+57]
86=proba2[56*256+59]
86=proba2[56*256+61]
72=proba2[56*256+63]
79=proba2[56*256+65]
68=proba2[56*256+66]
75=proba2[56*256+67]
79=proba2[56*256+68]
79=proba2[56*256+69]
68=proba2[56*256+70]
75=proba2[56*256+71]
79=proba2[56*256+72]
70=proba2[56*256+74]
75=proba2[56*256+75]
70=proba2[56*256+76]
66=proba2[56*256+77]
79=proba2[56*256+78]
75=proba2[56*256+79]
72=proba2[56*256+80]
79=proba2[56*256+81]
79=proba2[56*256+82]
68=proba2[56*256+83]
72=proba2[56*256+84]
86=proba2[56*256+85]
86=proba2[56*256+86]
86=proba2[56*256+87]
79=proba2[56*256+88]
79=proba2[56*256+89]
72=proba2[56*256+90]
79=proba2[56*256+94]
46=proba2[56*256+97]
46=proba2[56*256+98]
47=proba2[56*256+99]
52=proba2[56*256+100]
49=proba2[56*256+101]
54=proba2[56*256+102]
49=proba2[56*256+103]
50=proba2[56*256+104]
55=proba2[56*256+105]
49=proba2[56*256+106]
51=proba2[56*256+107]
53=proba2[56*256+108]
46=proba2[56*256+109]
56=proba2[56*256+110]
60=proba2[56*256+111]
50=proba2[56*256+112]
60=proba2[56*256+113]
53=proba2[56*256+114]
45=proba2[56*256+115]
50=proba2[56*256+116]
58=proba2[56*256+117]
58=proba2[56*256+118]
59=proba2[56*256+119]
64=proba2[56*256+120]
57=proba2[56*256+121]
55=proba2[56*256+122]
59=proba1[57]
82=proba2[57*256+32]
73=proba2[57*256+33]
89=proba2[57*256+36]
89=proba2[57*256+38]
78=proba2[57*256+42]
78=proba2[57*256+43]
71=proba2[57*256+45]
68=proba2[57*256+46]
26=proba2[57*256+48]
27=proba2[57*256+49]
30=proba2[57*256+50]
30=proba2[57*256+51]
29=proba2[57*256+52]
27=proba2[57*256+53]
24=proba2[57*256+54]
20=proba2[57*256+55]
15=proba2[57*256+56]
21=proba2[57*256+57]
89=proba2[57*256+61]
89=proba2[57*256+62]
67=proba2[57*256+65]
78=proba2[57*256+66]
75=proba2[57*256+68]
75=proba2[57*256+69]
82=proba2[57*256+71]
82=proba2[57*256+72]
78=proba2[57*256+73]
78=proba2[57*256+74]
71=proba2[57*256+75]
75=proba2[57*256+76]
71=proba2[57*256+77]
78=proba2[57*256+78]
82=proba2[57*256+79]
75=proba2[57*256+80]
70=proba2[57*256+82]
71=proba2[57*256+83]
75=proba2[57*256+84]
89=proba2[57*256+85]
82=proba2[57*256+86]
89=proba2[57*256+87]
75=proba2[57*256+89]
78=proba2[57*256+90]
49=proba2[57*256+97]
54=proba2[57*256+98]
49=proba2[57*256+99]
57=proba2[57*256+100]
58=proba2[57*256+101]
55=proba2[57*256+102]
55=proba2[57*256+103]
57=proba2[57*256+104]
57=proba2[57*256+105]
57=proba2[57*256+106]
61=proba2[57*256+107]
53=proba2[57*256+108]
51=proba2[57*256+109]
57=proba2[57*256+110]
63=proba2[57*256+111]
55=proba2[57*256+112]
66=proba2[57*256+113]
55=proba2[57*256+114]
51=proba2[57*256+115]
55=proba2[57*256+116]
63=proba2[57*256+117]
59=proba2[57*256+118]
61=proba2[57*256+119]
60=proba2[57*256+120]
64=proba2[57*256+121]
65=proba2[57*256+122]
97=proba1[59]
40=proba2[59*256+39]
40=proba2[59*256+42]
33=proba2[59*256+48]
40=proba2[59*256+50]
40=proba2[59*256+51]
33=proba2[59*256+52]
40=proba2[59*256+55]
33=proba2[59*256+57]
18=proba2[59*256+59]
40=proba2[59*256+61]
40=proba2[59*256+76]
33=proba2[59*256+77]
40=proba2[59*256+78]
40=proba2[59*256+80]
40=proba2[59*256+84]
40=proba2[59*256+97]
33=proba2[59*256+98]
33=proba2[59*256+100]
29=proba2[59*256+101]
33=proba2[59*256+102]
33=proba2[59*256+103]
40=proba2[59*256+104]
40=proba2[59*256+105]
33=proba2[59*256+108]
33=proba2[59*256+109]
26=proba2[59*256+110]
40=proba2[59*256+111]
40=proba2[59*256+112]
40=proba2[59*256+113]
33=proba2[59*256+114]
29=proba2[59*256+115]
111=proba1[60]
19=proba2[60*256+35]
19=proba2[60*256+49]
12=proba2[60*256+50]
19=proba2[60*256+51]
12=proba2[60*256+62]
95=proba1[61]
43=proba2[61*256+37]
43=proba2[61*256+40]
43=proba2[61*256+41]
32=proba2[61*256+42]
43=proba2[61*256+44]
43=proba2[61*256+45]
37=proba2[61*256+49]
24=proba2[61*256+50]
32=proba2[61*256+51]
37=proba2[61*256+53]
37=proba2[61*256+55]
43=proba2[61*256+56]
43=proba2[61*256+57]
43=proba2[61*256+59]
15=proba2[61*256+61]
43=proba2[61*256+63]
43=proba2[61*256+71]
43=proba2[61*256+83]
43=proba2[61*256+90]
43=proba2[61*256+94]
37=proba2[61*256+97]
37=proba2[61*256+98]
43=proba2[61*256+100]
43=proba2[61*256+102]
37=proba2[61*256+103]
43=proba2[61*256+107]
37=proba2[61*256+108]
20=proba2[61*256+109]
43=proba2[61*256+110]
43=proba2[61*256+111]
43=proba2[61*256+112]
43=proba2[61*256+113]
43=proba2[61*256+115]
32=proba2[61*256+116]
43=proba2[61*256+117]
43=proba2[61*256+119]
13=proba2[62*256+65]
13=proba2[62*256+98]
13=proba2[62*256+108]
13=proba2[62*256+115]
97=proba1[63]
25=proba2[63*256+33]
36=proba2[63*256+43]
29=proba2[63*256+46]
36=proba2[63*256+50]
36=proba2[63*256+54]
22=proba2[63*256+63]
36=proba2[63*256+65]
36=proba2[63*256+69]
36=proba2[63*256+73]
36=proba2[63*256+78]
36=proba2[63*256+89]
36=proba2[63*256+91]
36=proba2[63*256+98]
29=proba2[63*256+99]
36=proba2[63*256+101]
36=proba2[63*256+109]
29=proba2[63*256+110]
36=proba2[63*256+111]
36=proba2[63*256+113]
29=proba2[63*256+114]
29=proba2[63*256+115]
36=proba2[63*256+116]
36=proba2[63*256+119]
22=proba2[63*256+122]
82=proba1[64]
48=proba2[64*256+33]
37=proba2[64*256+35]
48=proba2[64*256+36]
48=proba2[64*256+46]
37=proba2[64*256+48]
32=proba2[64*256+49]
41=proba2[64*256+50]
37=proba2[64*256+51]
37=proba2[64*256+52]
48=proba2[64*256+53]
41=proba2[64*256+54]
41=proba2[64*256+55]
41=proba2[64*256+57]
48=proba2[64*256+63]
23=proba2[64*256+64]
48=proba2[64*256+66]
48=proba2[64*256+82]
41=proba2[64*256+91]
48=proba2[64*256+98]
34=proba2[64*256+99]
32=proba2[64*256+100]
48=proba2[64*256+101]
37=proba2[64*256+102]
34=proba2[64*256+103]
48=proba2[64*256+104]
41=proba2[64*256+106]
48=proba2[64*256+107]
25=proba2[64*256+108]
18=proba2[64*256+109]
34=proba2[64*256+110]
27=proba2[64*256+114]
32=proba2[64*256+115]
27=proba2[64*256+116]
37=proba2[64*256+118]
41=proba2[64*256+119]
48=proba2[64*256+120]
48=proba2[64*256+122]
59=proba1[65]
72=proba2[65*256+33]
65=proba2[65*256+43]
65=proba2[65*256+44]
65=proba2[65*256+45]
72=proba2[65*256+46]
54=proba2[65*256+48]
36=proba2[65*256+49]
39=proba2[65*256+50]
48=proba2[65*256+51]
51=proba2[65*256+52]
45=proba2[65*256+53]
47=proba2[65*256+54]
54=proba2[65*256+55]
51=proba2[65*256+56]
45=proba2[65*256+57]
72=proba2[65*256+59]
40=proba2[65*256+65]
35=proba2[65*256+66]
33=proba2[65*256+67]
35=proba2[65*256+68]
47=proba2[65*256+69]
43=proba2[65*256+70]
42=proba2[65*256+71]
49=proba2[65*256+72]
33=proba2[65*256+73]
56=proba2[65*256+74]
43=proba2[65*256+75]
25=proba2[65*256+76]
29=proba2[65*256+77]
22=proba2[65*256+78]
50=proba2[65*256+79]
40=proba2[65*256+80]
56=proba2[65*256+81]
25=proba2[65*256+82]
28=proba2[65*256+83]
30=proba2[65*256+84]
35=proba2[65*256+85]
43=proba2[65*256+86]
50=proba2[65*256+87]
49=proba2[65*256+88]
44=proba2[65*256+89]
35=proba2[65*256+90]
58=proba2[65*256+97]
49=proba2[65*256+98]
53=proba2[65*256+99]
44=proba2[65*256+100]
65=proba2[65*256+101]
72=proba2[65*256+102]
56=proba2[65*256+103]
65=proba2[65*256+104]
58=proba2[65*256+105]
58=proba2[65*256+107]
37=proba2[65*256+108]
56=proba2[65*256+109]
37=proba2[65*256+110]
54=proba2[65*256+112]
65=proba2[65*256+113]
43=proba2[65*256+114]
46=proba2[65*256+115]
51=proba2[65*256+116]
51=proba2[65*256+117]
61=proba2[65*256+118]
61=proba2[65*256+119]
65=proba2[65*256+120]
56=proba2[65*256+121]
72=proba2[65*256+122]
63=proba1[66]
63=proba2[66*256+35]
49=proba2[66*256+45]
43=proba2[66*256+48]
34=proba2[66*256+49]
40=proba2[66*256+50]
43=proba2[66*256+51]
41=proba2[66*256+52]
47=proba2[66*256+53]
39=proba2[66*256+54]
49=proba2[66*256+55]
41=proba2[66*256+56]
41=proba2[66*256+57]
63=proba2[66*256+64]
25=proba2[66*256+65]
38=proba2[66*256+66]
36=proba2[66*256+67]
33=proba2[66*256+68]
25=proba2[66*256+69]
49=proba2[66*256+70]
39=proba2[66*256+71]
63=proba2[66*256+72]
30=proba2[66*256+73]
47=proba2[66*256+74]
52=proba2[66*256+75]
35=proba2[66*256+76]
41=proba2[66*256+77]
43=proba2[66*256+78]
25=proba2[66*256+79]
63=proba2[66*256+80]
63=proba2[66*256+81]
34=proba2[66*256+82]
45=proba2[66*256+83]
52=proba2[66*256+84]
39=proba2[66*256+85]
47=proba2[66*256+86]
56=proba2[66*256+87]
56=proba2[66*256+88]
49=proba2[66*256+89]
43=proba2[66*256+90]
29=proba2[66*256+97]
63=proba2[66*256+98]
63=proba2[66*256+99]
35=proba2[66*256+101]
63=proba2[66*256+104]
35=proba2[66*256+105]
49=proba2[66*256+108]
63=proba2[66*256+110]
29=proba2[66*256+111]
52=proba2[66*256+112]
63=proba2[66*256+113]
35=proba2[66*256+114]
56=proba2[66*256+115]
56=proba2[66*256+116]
39=proba2[66*256+117]
56=proba2[66*256+118]
56=proba2[66*256+119]
56=proba2[66*256+120]
56=proba2[66*256+122]
61=proba1[67]
65=proba2[67*256+32]
65=proba2[67*256+39]
65=proba2[67*256+41]
59=proba2[67*256+46]
48=proba2[67*256+48]
37=proba2[67*256+49]
36=proba2[67*256+50]
39=proba2[67*256+51]
43=proba2[67*256+52]
42=proba2[67*256+53]
42=proba2[67*256+54]
65=proba2[67*256+55]
54=proba2[67*256+56]
48=proba2[67*256+57]
24=proba2[67*256+65]
42=proba2[67*256+66]
41=proba2[67*256+67]
43=proba2[67*256+68]
28=proba2[67*256+69]
49=proba2[67*256+70]
49=proba2[67*256+71]
23=proba2[67*256+72]
32=proba2[67*256+73]
65=proba2[67*256+74]
33=proba2[67*256+75]
38=proba2[67*256+76]
43=proba2[67*256+77]
49=proba2[67*256+78]
22=proba2[67*256+79]
42=proba2[67*256+80]
59=proba2[67*256+81]
31=proba2[67*256+82]
40=proba2[67*256+83]
41=proba2[67*256+84]
42=proba2[67*256+85]
54=proba2[67*256+86]
54=proba2[67*256+88]
54=proba2[67*256+89]
59=proba2[67*256+90]
65=proba2[67*256+94]
33=proba2[67*256+97]
65=proba2[67*256+98]
52=proba2[67*256+99]
41=proba2[67*256+101]
65=proba2[67*256+102]
54=proba2[67*256+103]
33=proba2[67*256+104]
45=proba2[67*256+105]
65=proba2[67*256+106]
65=proba2[67*256+107]
38=proba2[67*256+108]
48=proba2[67*256+109]
31=proba2[67*256+111]
65=proba2[67*256+112]
65=proba2[67*256+113]
43=proba2[67*256+114]
54=proba2[67*256+115]
65=proba2[67*256+116]
54=proba2[67*256+117]
41=proba2[67*256+121]
65=proba1[68]
56=proba2[68*256+38]
63=proba2[68*256+43]
40=proba2[68*256+48]
36=proba2[68*256+49]
41=proba2[68*256+50]
42=proba2[68*256+51]
49=proba2[68*256+52]
52=proba2[68*256+53]
49=proba2[68*256+54]
45=proba2[68*256+55]
43=proba2[68*256+56]
40=proba2[68*256+57]
63=proba2[68*256+59]
63=proba2[68*256+64]
24=proba2[68*256+65]
45=proba2[68*256+66]
45=proba2[68*256+67]
41=proba2[68*256+68]
23=proba2[68*256+69]
47=proba2[68*256+70]
52=proba2[68*256+71]
49=proba2[68*256+72]
25=proba2[68*256+73]
36=proba2[68*256+74]
47=proba2[68*256+75]
42=proba2[68*256+76]
40=proba2[68*256+77]
56=proba2[68*256+78]
27=proba2[68*256+79]
47=proba2[68*256+80]
30=proba2[68*256+82]
39=proba2[68*256+83]
47=proba2[68*256+84]
38=proba2[68*256+85]
49=proba2[68*256+86]
52=proba2[68*256+87]
49=proba2[68*256+89]
47=proba2[68*256+90]
63=proba2[68*256+95]
32=proba2[68*256+97]
49=proba2[68*256+100]
31=proba2[68*256+101]
63=proba2[68*256+102]
63=proba2[68*256+103]
63=proba2[68*256+104]
33=proba2[68*256+105]
49=proba2[68*256+106]
56=proba2[68*256+107]
63=proba2[68*256+108]
52=proba2[68*256+109]
56=proba2[68*256+110]
33=proba2[68*256+111]
49=proba2[68*256+112]
36=proba2[68*256+114]
47=proba2[68*256+115]
63=proba2[68*256+116]
38=proba2[68*256+117]
56=proba2[68*256+118]
63=proba2[68*256+119]
45=proba2[68*256+120]
56=proba2[68*256+121]
69=proba1[69]
68=proba2[69*256+42]
68=proba2[69*256+45]
68=proba2[69*256+46]
46=proba2[69*256+48]
35=proba2[69*256+49]
41=proba2[69*256+50]
52=proba2[69*256+51]
50=proba2[69*256+52]
43=proba2[69*256+53]
47=proba2[69*256+54]
57=proba2[69*256+55]
52=proba2[69*256+56]
39=proba2[69*256+57]
35=proba2[69*256+65]
39=proba2[69*256+66]
35=proba2[69*256+67]
35=proba2[69*256+68]
44=proba2[69*256+69]
49=proba2[69*256+70]
41=proba2[69*256+71]
52=proba2[69*256+72]
47=proba2[69*256+73]
52=proba2[69*256+74]
46=proba2[69*256+75]
22=proba2[69*256+76]
32=proba2[69*256+77]
26=proba2[69*256+78]
46=proba2[69*256+79]
38=proba2[69*256+80]
18=proba2[69*256+82]
27=proba2[69*256+83]
27=proba2[69*256+84]
39=proba2[69*256+85]
44=proba2[69*256+86]
50=proba2[69*256+87]
40=proba2[69*256+88]
45=proba2[69*256+89]
52=proba2[69*256+90]
54=proba2[69*256+97]
54=proba2[69*256+98]
50=proba2[69*256+99]
54=proba2[69*256+100]
57=proba2[69*256+101]
61=proba2[69*256+102]
61=proba2[69*256+103]
57=proba2[69*256+104]
57=proba2[69*256+105]
61=proba2[69*256+106]
61=proba2[69*256+107]
38=proba2[69*256+108]
49=proba2[69*256+109]
43=proba2[69*256+110]
61=proba2[69*256+111]
68=proba2[69*256+112]
57=proba2[69*256+114]
54=proba2[69*256+115]
50=proba2[69*256+116]
50=proba2[69*256+117]
57=proba2[69*256+118]
68=proba2[69*256+119]
54=proba2[69*256+120]
68=proba2[69*256+121]
68=proba2[69*256+122]
66=proba1[70]
59=proba2[70*256+35]
59=proba2[70*256+38]
59=proba2[70*256+44]
59=proba2[70*256+45]
59=proba2[70*256+46]
43=proba2[70*256+48]
35=proba2[70*256+49]
37=proba2[70*256+50]
40=proba2[70*256+51]
48=proba2[70*256+52]
45=proba2[70*256+53]
40=proba2[70*256+54]
48=proba2[70*256+55]
48=proba2[70*256+56]
40=proba2[70*256+57]
23=proba2[70*256+65]
41=proba2[70*256+66]
41=proba2[70*256+67]
37=proba2[70*256+68]
33=proba2[70*256+69]
35=proba2[70*256+70]
43=proba2[70*256+71]
59=proba2[70*256+72]
27=proba2[70*256+73]
41=proba2[70*256+74]
48=proba2[70*256+75]
32=proba2[70*256+76]
41=proba2[70*256+77]
59=proba2[70*256+78]
31=proba2[70*256+79]
48=proba2[70*256+80]
59=proba2[70*256+81]
29=proba2[70*256+82]
37=proba2[70*256+83]
41=proba2[70*256+84]
36=proba2[70*256+85]
43=proba2[70*256+86]
48=proba2[70*256+87]
48=proba2[70*256+88]
59=proba2[70*256+89]
59=proba2[70*256+90]
59=proba2[70*256+93]
59=proba2[70*256+95]
30=proba2[70*256+97]
48=proba2[70*256+99]
59=proba2[70*256+100]
36=proba2[70*256+101]
45=proba2[70*256+102]
52=proba2[70*256+103]
41=proba2[70*256+105]
59=proba2[70*256+107]
33=proba2[70*256+108]
52=proba2[70*256+109]
52=proba2[70*256+110]
38=proba2[70*256+111]
30=proba2[70*256+114]
52=proba2[70*256+116]
41=proba2[70*256+117]
59=proba2[70*256+121]
59=proba2[70*256+122]
66=proba1[71]
54=proba2[71*256+32]
54=proba2[71*256+33]
54=proba2[71*256+42]
61=proba2[71*256+45]
43=proba2[71*256+48]
37=proba2[71*256+49]
39=proba2[71*256+50]
41=proba2[71*256+51]
47=proba2[71*256+52]
47=proba2[71*256+53]
54=proba2[71*256+54]
37=proba2[71*256+55]
54=proba2[71*256+56]
37=proba2[71*256+57]
23=proba2[71*256+65]
38=proba2[71*256+66]
43=proba2[71*256+67]
45=proba2[71*256+68]
27=proba2[71*256+69]
47=proba2[71*256+70]
38=proba2[71*256+71]
39=proba2[71*256+72]
31=proba2[71*256+73]
50=proba2[71*256+74]
54=proba2[71*256+75]
41=proba2[71*256+76]
45=proba2[71*256+77]
41=proba2[71*256+78]
27=proba2[71*256+79]
40=proba2[71*256+80]
54=proba2[71*256+81]
35=proba2[71*256+82]
47=proba2[71*256+83]
43=proba2[71*256+84]
35=proba2[71*256+85]
50=proba2[71*256+86]
50=proba2[71*256+87]
45=proba2[71*256+88]
50=proba2[71*256+89]
61=proba2[71*256+90]
30=proba2[71*256+97]
54=proba2[71*256+98]
50=proba2[71*256+100]
47=proba2[71*256+101]
61=proba2[71*256+102]
47=proba2[71*256+103]
43=proba2[71*256+104]
35=proba2[71*256+105]
54=proba2[71*256+106]
50=proba2[71*256+107]
40=proba2[71*256+108]
54=proba2[71*256+109]
54=proba2[71*256+110]
30=proba2[71*256+111]
50=proba2[71*256+112]
41=proba2[71*256+114]
61=proba2[71*256+116]
31=proba2[71*256+117]
54=proba2[71*256+119]
54=proba2[71*256+120]
50=proba2[71*256+121]
72=proba1[72]
58=proba2[72*256+32]
58=proba2[72*256+33]
58=proba2[72*256+44]
58=proba2[72*256+45]
58=proba2[72*256+48]
37=proba2[72*256+49]
37=proba2[72*256+50]
40=proba2[72*256+51]
51=proba2[72*256+52]
58=proba2[72*256+53]
51=proba2[72*256+54]
44=proba2[72*256+55]
47=proba2[72*256+56]
42=proba2[72*256+57]
20=proba2[72*256+65]
42=proba2[72*256+66]
44=proba2[72*256+67]
42=proba2[72*256+68]
21=proba2[72*256+69]
42=proba2[72*256+70]
58=proba2[72*256+71]
42=proba2[72*256+72]
26=proba2[72*256+73]
51=proba2[72*256+74]
39=proba2[72*256+75]
47=proba2[72*256+76]
36=proba2[72*256+77]
47=proba2[72*256+78]
26=proba2[72*256+79]
51=proba2[72*256+80]
47=proba2[72*256+81]
35=proba2[72*256+82]
47=proba2[72*256+83]
47=proba2[72*256+84]
37=proba2[72*256+85]
58=proba2[72*256+86]
51=proba2[72*256+87]
44=proba2[72*256+88]
39=proba2[72*256+89]
42=proba2[72*256+90]
51=proba2[72*256+95]
32=proba2[72*256+97]
51=proba2[72*256+98]
58=proba2[72*256+99]
47=proba2[72*256+100]
32=proba2[72*256+101]
58=proba2[72*256+102]
37=proba2[72*256+105]
51=proba2[72*256+106]
58=proba2[72*256+107]
58=proba2[72*256+108]
58=proba2[72*256+110]
36=proba2[72*256+111]
51=proba2[72*256+114]
58=proba2[72*256+115]
58=proba2[72*256+116]
42=proba2[72*256+117]
51=proba2[72*256+121]
51=proba2[72*256+122]
58=proba2[72*256+125]
75=proba1[73]
67=proba2[73*256+36]
67=proba2[73*256+38]
67=proba2[73*256+46]
49=proba2[73*256+48]
41=proba2[73*256+49]
44=proba2[73*256+50]
51=proba2[73*256+51]
49=proba2[73*256+52]
60=proba2[73*256+53]
46=proba2[73*256+54]
46=proba2[73*256+55]
56=proba2[73*256+56]
53=proba2[73*256+57]
29=proba2[73*256+65]
45=proba2[73*256+66]
26=proba2[73*256+67]
35=proba2[73*256+68]
21=proba2[73*256+69]
44=proba2[73*256+70]
36=proba2[73*256+71]
67=proba2[73*256+72]
42=proba2[73*256+73]
60=proba2[73*256+74]
37=proba2[73*256+75]
28=proba2[73*256+76]
30=proba2[73*256+77]
21=proba2[73*256+78]
33=proba2[73*256+79]
43=proba2[73*256+80]
44=proba2[73*256+81]
31=proba2[73*256+82]
25=proba2[73*256+83]
27=proba2[73*256+84]
56=proba2[73*256+85]
39=proba2[73*256+86]
53=proba2[73*256+87]
42=proba2[73*256+88]
67=proba2[73*256+89]
45=proba2[73*256+90]
53=proba2[73*256+97]
56=proba2[73*256+99]
53=proba2[73*256+100]
60=proba2[73*256+102]
60=proba2[73*256+103]
67=proba2[73*256+104]
67=proba2[73*256+105]
67=proba2[73*256+107]
56=proba2[73*256+108]
53=proba2[73*256+109]
45=proba2[73*256+110]
60=proba2[73*256+112]
60=proba2[73*256+113]
53=proba2[73*256+114]
49=proba2[73*256+115]
67=proba2[73*256+116]
60=proba2[73*256+118]
67=proba2[73*256+119]
67=proba2[73*256+120]
60=proba2[73*256+122]
67=proba1[74]
57=proba2[74*256+42]
57=proba2[74*256+46]
37=proba2[74*256+48]
46=proba2[74*256+49]
37=proba2[74*256+50]
46=proba2[74*256+51]
57=proba2[74*256+52]
50=proba2[74*256+53]
57=proba2[74*256+54]
43=proba2[74*256+55]
57=proba2[74*256+56]
50=proba2[74*256+57]
50=proba2[74*256+64]
28=proba2[74*256+65]
39=proba2[74*256+66]
39=proba2[74*256+67]
46=proba2[74*256+68]
26=proba2[74*256+69]
36=proba2[74*256+70]
46=proba2[74*256+71]
43=proba2[74*256+72]
32=proba2[74*256+73]
36=proba2[74*256+74]
46=proba2[74*256+75]
39=proba2[74*256+76]
39=proba2[74*256+77]
57=proba2[74*256+78]
21=proba2[74*256+79]
30=proba2[74*256+80]
50=proba2[74*256+81]
39=proba2[74*256+82]
43=proba2[74*256+83]
46=proba2[74*256+84]
27=proba2[74*256+85]
57=proba2[74*256+87]
57=proba2[74*256+89]
50=proba2[74*256+90]
57=proba2[74*256+95]
31=proba2[74*256+97]
50=proba2[74*256+98]
43=proba2[74*256+99]
57=proba2[74*256+100]
34=proba2[74*256+101]
50=proba2[74*256+102]
46=proba2[74*256+104]
50=proba2[74*256+105]
50=proba2[74*256+106]
57=proba2[74*256+107]
50=proba2[74*256+109]
57=proba2[74*256+110]
32=proba2[74*256+111]
57=proba2[74*256+112]
57=proba2[74*256+114]
57=proba2[74*256+116]
30=proba2[74*256+117]
50=proba2[74*256+118]
50=proba2[74*256+119]
57=proba2[74*256+121]
70=proba1[75]
56=proba2[75*256+39]
56=proba2[75*256+46]
56=proba2[75*256+48]
35=proba2[75*256+49]
34=proba2[75*256+50]
49=proba2[75*256+51]
40=proba2[75*256+52]
45=proba2[75*256+53]
38=proba2[75*256+54]
49=proba2[75*256+55]
40=proba2[75*256+56]
45=proba2[75*256+57]
56=proba2[75*256+63]
56=proba2[75*256+64]
22=proba2[75*256+65]
42=proba2[75*256+66]
56=proba2[75*256+67]
49=proba2[75*256+68]
30=proba2[75*256+69]
45=proba2[75*256+70]
56=proba2[75*256+71]
45=proba2[75*256+72]
23=proba2[75*256+73]
45=proba2[75*256+74]
45=proba2[75*256+75]
35=proba2[75*256+76]
40=proba2[75*256+77]
49=proba2[75*256+78]
30=proba2[75*256+79]
56=proba2[75*256+80]
56=proba2[75*256+81]
37=proba2[75*256+82]
45=proba2[75*256+83]
45=proba2[75*256+84]
49=proba2[75*256+85]
56=proba2[75*256+86]
49=proba2[75*256+87]
56=proba2[75*256+88]
42=proba2[75*256+89]
56=proba2[75*256+90]
56=proba2[75*256+95]
26=proba2[75*256+97]
49=proba2[75*256+98]
45=proba2[75*256+100]
31=proba2[75*256+101]
49=proba2[75*256+102]
49=proba2[75*256+103]
40=proba2[75*256+104]
34=proba2[75*256+105]
49=proba2[75*256+107]
56=proba2[75*256+108]
56=proba2[75*256+110]
33=proba2[75*256+111]
49=proba2[75*256+112]
49=proba2[75*256+113]
42=proba2[75*256+114]
56=proba2[75*256+115]
56=proba2[75*256+116]
45=proba2[75*256+117]
56=proba2[75*256+118]
56=proba2[75*256+119]
49=proba2[75*256+120]
56=proba2[75*256+121]
49=proba2[75*256+122]
56=proba2[75*256+124]
65=proba1[76]
66=proba2[76*256+32]
66=proba2[76*256+41]
66=proba2[76*256+45]
55=proba2[76*256+46]
44=proba2[76*256+48]
41=proba2[76*256+49]
45=proba2[76*256+50]
52=proba2[76*256+51]
46=proba2[76*256+52]
46=proba2[76*256+53]
50=proba2[76*256+54]
55=proba2[76*256+55]
59=proba2[76*256+56]
48=proba2[76*256+57]
66=proba2[76*256+64]
21=proba2[76*256+65]
45=proba2[76*256+66]
44=proba2[76*256+67]
36=proba2[76*256+68]
21=proba2[76*256+69]
44=proba2[76*256+70]
45=proba2[76*256+71]
46=proba2[76*256+72]
20=proba2[76*256+73]
66=proba2[76*256+74]
52=proba2[76*256+75]
28=proba2[76*256+76]
36=proba2[76*256+77]
24=proba2[76*256+79]
39=proba2[76*256+80]
52=proba2[76*256+82]
43=proba2[76*256+83]
40=proba2[76*256+84]
36=proba2[76*256+85]
44=proba2[76*256+86]
59=proba2[76*256+87]
41=proba2[76*256+89]
66=proba2[76*256+90]
66=proba2[76*256+93]
32=proba2[76*256+97]
66=proba2[76*256+98]
55=proba2[76*256+99]
55=proba2[76*256+100]
36=proba2[76*256+101]
50=proba2[76*256+102]
66=proba2[76*256+103]
59=proba2[76*256+104]
38=proba2[76*256+105]
59=proba2[76*256+106]
55=proba2[76*256+109]
66=proba2[76*256+110]
35=proba2[76*256+111]
66=proba2[76*256+112]
66=proba2[76*256+113]
66=proba2[76*256+114]
66=proba2[76*256+115]
55=proba2[76*256+116]
41=proba2[76*256+117]
66=proba2[76*256+118]
66=proba2[76*256+119]
55=proba2[76*256+121]
50=proba2[76*256+122]
58=proba1[77]
60=proba2[77*256+32]
67=proba2[77*256+33]
60=proba2[77*256+42]
60=proba2[77*256+43]
60=proba2[77*256+44]
51=proba2[77*256+45]
46=proba2[77*256+48]
39=proba2[77*256+49]
44=proba2[77*256+50]
46=proba2[77*256+51]
60=proba2[77*256+52]
51=proba2[77*256+53]
49=proba2[77*256+54]
46=proba2[77*256+55]
47=proba2[77*256+56]
46=proba2[77*256+57]
60=proba2[77*256+63]
18=proba2[77*256+65]
39=proba2[77*256+66]
42=proba2[77*256+67]
45=proba2[77*256+68]
25=proba2[77*256+69]
49=proba2[77*256+70]
44=proba2[77*256+71]
60=proba2[77*256+72]
26=proba2[77*256+73]
53=proba2[77*256+74]
51=proba2[77*256+75]
46=proba2[77*256+76]
34=proba2[77*256+77]
45=proba2[77*256+78]
27=proba2[77*256+79]
38=proba2[77*256+80]
56=proba2[77*256+81]
46=proba2[77*256+82]
51=proba2[77*256+83]
47=proba2[77*256+84]
44=proba2[77*256+85]
51=proba2[77*256+86]
49=proba2[77*256+87]
67=proba2[77*256+88]
36=proba2[77*256+89]
53=proba2[77*256+90]
67=proba2[77*256+95]
22=proba2[77*256+97]
51=proba2[77*256+98]
51=proba2[77*256+99]
32=proba2[77*256+101]
60=proba2[77*256+102]
56=proba2[77*256+103]
67=proba2[77*256+104]
32=proba2[77*256+105]
56=proba2[77*256+106]
56=proba2[77*256+107]
60=proba2[77*256+108]
60=proba2[77*256+109]
56=proba2[77*256+110]
35=proba2[77*256+111]
67=proba2[77*256+112]
56=proba2[77*256+114]
67=proba2[77*256+115]
56=proba2[77*256+116]
46=proba2[77*256+117]
67=proba2[77*256+119]
67=proba2[77*256+120]
39=proba2[77*256+121]
67=proba2[77*256+122]
69=proba1[78]
64=proba2[78*256+33]
64=proba2[78*256+43]
53=proba2[78*256+45]
57=proba2[78*256+46]
57=proba2[78*256+48]
39=proba2[78*256+49]
40=proba2[78*256+50]
46=proba2[78*256+51]
45=proba2[78*256+52]
45=proba2[78*256+53]
48=proba2[78*256+54]
46=proba2[78*256+55]
50=proba2[78*256+56]
50=proba2[78*256+57]
64=proba2[78*256+61]
64=proba2[78*256+64]
26=proba2[78*256+65]
50=proba2[78*256+66]
33=proba2[78*256+67]
26=proba2[78*256+68]
19=proba2[78*256+69]
39=proba2[78*256+70]
34=proba2[78*256+71]
50=proba2[78*256+72]
25=proba2[78*256+73]
48=proba2[78*256+74]
57=proba2[78*256+75]
48=proba2[78*256+76]
50=proba2[78*256+77]
30=proba2[78*256+78]
28=proba2[78*256+79]
53=proba2[78*256+80]
64=proba2[78*256+81]
44=proba2[78*256+83]
26=proba2[78*256+84]
40=proba2[78*256+85]
64=proba2[78*256+87]
46=proba2[78*256+88]
41=proba2[78*256+89]
48=proba2[78*256+90]
33=proba2[78*256+97]
53=proba2[78*256+98]
57=proba2[78*256+99]
64=proba2[78*256+100]
39=proba2[78*256+101]
64=proba2[78*256+102]
48=proba2[78*256+103]
64=proba2[78*256+104]
34=proba2[78*256+105]
64=proba2[78*256+107]
64=proba2[78*256+109]
64=proba2[78*256+110]
37=proba2[78*256+111]
46=proba2[78*256+112]
57=proba2[78*256+115]
53=proba2[78*256+116]
48=proba2[78*256+117]
64=proba2[78*256+119]
64=proba2[78*256+121]
57=proba2[78*256+122]
64=proba2[78*256+125]
77=proba1[79]
66=proba2[79*256+32]
66=proba2[79*256+46]
45=proba2[79*256+48]
43=proba2[79*256+49]
44=proba2[79*256+50]
48=proba2[79*256+51]
66=proba2[79*256+52]
50=proba2[79*256+53]
50=proba2[79*256+54]
59=proba2[79*256+55]
55=proba2[79*256+56]
44=proba2[79*256+57]
55=proba2[79*256+61]
48=proba2[79*256+65]
38=proba2[79*256+66]
31=proba2[79*256+67]
44=proba2[79*256+68]
50=proba2[79*256+69]
50=proba2[79*256+70]
40=proba2[79*256+71]
50=proba2[79*256+72]
36=proba2[79*256+73]
44=proba2[79*256+74]
52=proba2[79*256+75]
26=proba2[79*256+76]
27=proba2[79*256+77]
20=proba2[79*256+78]
34=proba2[79*256+79]
35=proba2[79*256+80]
24=proba2[79*256+82]
30=proba2[79*256+83]
30=proba2[79*256+84]
18=proba2[79*256+85]
47=proba2[79*256+86]
55=proba2[79*256+87]
50=proba2[79*256+88]
42=proba2[79*256+89]
52=proba2[79*256+90]
66=proba2[79*256+93]
59=proba2[79*256+98]
59=proba2[79*256+99]
66=proba2[79*256+103]
66=proba2[79*256+104]
59=proba2[79*256+105]
66=proba2[79*256+106]
66=proba2[79*256+107]
52=proba2[79*256+108]
59=proba2[79*256+109]
50=proba2[79*256+110]
66=proba2[79*256+112]
59=proba2[79*256+114]
66=proba2[79*256+115]
55=proba2[79*256+116]
66=proba2[79*256+117]
66=proba2[79*256+120]
66=proba2[79*256+121]
55=proba2[79*256+122]
63=proba1[80]
56=proba2[80*256+45]
41=proba2[80*256+48]
36=proba2[80*256+49]
36=proba2[80*256+50]
43=proba2[80*256+51]
41=proba2[80*256+52]
52=proba2[80*256+53]
46=proba2[80*256+54]
52=proba2[80*256+55]
63=proba2[80*256+56]
46=proba2[80*256+57]
63=proba2[80*256+61]
63=proba2[80*256+63]
23=proba2[80*256+65]
49=proba2[80*256+66]
45=proba2[80*256+67]
42=proba2[80*256+68]
27=proba2[80*256+69]
49=proba2[80*256+70]
39=proba2[80*256+71]
29=proba2[80*256+72]
30=proba2[80*256+73]
45=proba2[80*256+74]
49=proba2[80*256+75]
36=proba2[80*256+76]
42=proba2[80*256+77]
46=proba2[80*256+78]
27=proba2[80*256+79]
38=proba2[80*256+80]
56=proba2[80*256+81]
32=proba2[80*256+82]
39=proba2[80*256+83]
39=proba2[80*256+84]
37=proba2[80*256+85]
42=proba2[80*256+86]
56=proba2[80*256+87]
63=proba2[80*256+88]
41=proba2[80*256+89]
63=proba2[80*256+90]
28=proba2[80*256+97]
63=proba2[80*256+98]
34=proba2[80*256+101]
63=proba2[80*256+103]
36=proba2[80*256+104]
36=proba2[80*256+105]
56=proba2[80*256+108]
56=proba2[80*256+109]
52=proba2[80*256+110]
29=proba2[80*256+111]
63=proba2[80*256+113]
42=proba2[80*256+114]
63=proba2[80*256+115]
63=proba2[80*256+116]
46=proba2[80*256+117]
63=proba2[80*256+118]
63=proba2[80*256+119]
56=proba2[80*256+120]
63=proba2[80*256+121]
84=proba1[81]
47=proba2[81*256+48]
47=proba2[81*256+49]
40=proba2[81*256+50]
47=proba2[81*256+51]
36=proba2[81*256+52]
33=proba2[81*256+53]
47=proba2[81*256+54]
33=proba2[81*256+55]
47=proba2[81*256+56]
33=proba2[81*256+57]
33=proba2[81*256+65]
47=proba2[81*256+67]
40=proba2[81*256+68]
40=proba2[81*256+69]
40=proba2[81*256+70]
33=proba2[81*256+71]
40=proba2[81*256+73]
40=proba2[81*256+74]
40=proba2[81*256+76]
40=proba2[81*256+77]
47=proba2[81*256+79]
40=proba2[81*256+80]
47=proba2[81*256+81]
40=proba2[81*256+82]
40=proba2[81*256+83]
40=proba2[81*256+84]
20=proba2[81*256+85]
33=proba2[81*256+87]
33=proba2[81*256+89]
47=proba2[81*256+90]
47=proba2[81*256+97]
40=proba2[81*256+98]
47=proba2[81*256+99]
40=proba2[81*256+102]
47=proba2[81*256+103]
40=proba2[81*256+104]
47=proba2[81*256+107]
47=proba2[81*256+109]
47=proba2[81*256+110]
47=proba2[81*256+112]
40=proba2[81*256+113]
47=proba2[81*256+114]
40=proba2[81*256+116]
26=proba2[81*256+117]
29=proba2[81*256+119]
47=proba2[81*256+122]
69=proba1[82]
55=proba2[82*256+32]
66=proba2[82*256+35]
66=proba2[82*256+42]
66=proba2[82*256+43]
55=proba2[82*256+48]
36=proba2[82*256+49]
38=proba2[82*256+50]
48=proba2[82*256+51]
45=proba2[82*256+52]
48=proba2[82*256+53]
59=proba2[82*256+54]
45=proba2[82*256+55]
52=proba2[82*256+56]
48=proba2[82*256+57]
21=proba2[82*256+65]
44=proba2[82*256+66]
38=proba2[82*256+67]
34=proba2[82*256+68]
22=proba2[82*256+69]
47=proba2[82*256+70]
38=proba2[82*256+71]
66=proba2[82*256+72]
20=proba2[82*256+73]
52=proba2[82*256+74]
52=proba2[82*256+75]
39=proba2[82*256+76]
39=proba2[82*256+77]
44=proba2[82*256+78]
24=proba2[82*256+79]
45=proba2[82*256+80]
52=proba2[82*256+81]
36=proba2[82*256+82]
33=proba2[82*256+83]
32=proba2[82*256+84]
36=proba2[82*256+85]
47=proba2[82*256+86]
59=proba2[82*256+87]
59=proba2[82*256+88]
41=proba2[82*256+89]
66=proba2[82*256+90]
66=proba2[82*256+93]
66=proba2[82*256+96]
40=proba2[82*256+97]
55=proba2[82*256+100]
37=proba2[82*256+101]
59=proba2[82*256+102]
66=proba2[82*256+103]
66=proba2[82*256+104]
41=proba2[82*256+105]
66=proba2[82*256+106]
66=proba2[82*256+107]
66=proba2[82*256+108]
36=proba2[82*256+111]
55=proba2[82*256+113]
55=proba2[82*256+114]
59=proba2[82*256+115]
59=proba2[82*256+116]
43=proba2[82*256+117]
66=proba2[82*256+118]
59=proba2[82*256+119]
66=proba2[82*256+121]
66=proba2[82*256+122]
61=proba1[83]
59=proba2[83*256+32]
66=proba2[83*256+35]
66=proba2[83*256+38]
66=proba2[83*256+41]
66=proba2[83*256+42]
66=proba2[83*256+45]
44=proba2[83*256+48]
33=proba2[83*256+49]
43=proba2[83*256+50]
48=proba2[83*256+51]
48=proba2[83*256+52]
50=proba2[83*256+53]
50=proba2[83*256+54]
46=proba2[83*256+55]
50=proba2[83*256+56]
42=proba2[83*256+57]
66=proba2[83*256+64]
25=proba2[83*256+65]
46=proba2[83*256+66]
33=proba2[83*256+67]
50=proba2[83*256+68]
25=proba2[83*256+69]
55=proba2[83*256+70]
66=proba2[83*256+71]
40=proba2[83*256+72]
32=proba2[83*256+73]
55=proba2[83*256+74]
50=proba2[83*256+75]
40=proba2[83*256+76]
45=proba2[83*256+77]
42=proba2[83*256+78]
35=proba2[83*256+79]
35=proba2[83*256+80]
46=proba2[83*256+81]
43=proba2[83*256+82]
27=proba2[83*256+83]
25=proba2[83*256+84]
38=proba2[83*256+85]
50=proba2[83*256+86]
42=proba2[83*256+87]
50=proba2[83*256+88]
39=proba2[83*256+89]
55=proba2[83*256+90]
31=proba2[83*256+97]
66=proba2[83*256+98]
44=proba2[83*256+99]
59=proba2[83*256+100]
36=proba2[83*256+101]
59=proba2[83*256+102]
66=proba2[83*256+103]
42=proba2[83*256+104]
40=proba2[83*256+105]
66=proba2[83*256+106]
50=proba2[83*256+107]
59=proba2[83*256+108]
48=proba2[83*256+109]
52=proba2[83*256+110]
33=proba2[83*256+111]
48=proba2[83*256+112]
55=proba2[83*256+113]
66=proba2[83*256+114]
66=proba2[83*256+115]
38=proba2[83*256+116]
41=proba2[83*256+117]
59=proba2[83*256+119]
66=proba2[83*256+120]
48=proba2[83*256+121]
64=proba1[84]
66=proba2[84*256+33]
66=proba2[84*256+39]
66=proba2[84*256+43]
66=proba2[84*256+44]
59=proba2[84*256+45]
52=proba2[84*256+48]
42=proba2[84*256+49]
39=proba2[84*256+50]
50=proba2[84*256+51]
46=proba2[84*256+52]
48=proba2[84*256+53]
44=proba2[84*256+54]
48=proba2[84*256+55]
48=proba2[84*256+56]
44=proba2[84*256+57]
24=proba2[84*256+65]
48=proba2[84*256+66]
37=proba2[84*256+67]
48=proba2[84*256+68]
22=proba2[84*256+69]
50=proba2[84*256+70]
48=proba2[84*256+71]
33=proba2[84*256+72]
24=proba2[84*256+73]
50=proba2[84*256+74]
66=proba2[84*256+75]
44=proba2[84*256+76]
43=proba2[84*256+77]
45=proba2[84*256+78]
23=proba2[84*256+79]
52=proba2[84*256+80]
31=proba2[84*256+82]
42=proba2[84*256+83]
31=proba2[84*256+84]
39=proba2[84*256+85]
55=proba2[84*256+86]
50=proba2[84*256+87]
59=proba2[84*256+88]
39=proba2[84*256+89]
52=proba2[84*256+90]
37=proba2[84*256+97]
50=proba2[84*256+98]
55=proba2[84*256+99]
66=proba2[84*256+100]
39=proba2[84*256+101]
52=proba2[84*256+102]
66=proba2[84*256+103]
33=proba2[84*256+104]
37=proba2[84*256+105]
66=proba2[84*256+106]
66=proba2[84*256+108]
59=proba2[84*256+110]
34=proba2[84*256+111]
48=proba2[84*256+112]
55=proba2[84*256+113]
41=proba2[84*256+114]
66=proba2[84*256+115]
55=proba2[84*256+116]
42=proba2[84*256+117]
66=proba2[84*256+118]
59=proba2[84*256+119]
59=proba2[84*256+120]
46=proba2[84*256+121]
81=proba1[85]
61=proba2[85*256+42]
61=proba2[85*256+45]
61=proba2[85*256+48]
37=proba2[85*256+49]
43=proba2[85*256+50]
50=proba2[85*256+51]
44=proba2[85*256+52]
54=proba2[85*256+53]
50=proba2[85*256+54]
40=proba2[85*256+55]
43=proba2[85*256+57]
61=proba2[85*256+64]
40=proba2[85*256+65]
32=proba2[85*256+66]
28=proba2[85*256+67]
28=proba2[85*256+68]
30=proba2[85*256+69]
44=proba2[85*256+70]
43=proba2[85*256+71]
54=proba2[85*256+72]
31=proba2[85*256+73]
54=proba2[85*256+74]
47=proba2[85*256+75]
25=proba2[85*256+76]
34=proba2[85*256+77]
28=proba2[85*256+78]
54=proba2[85*256+79]
39=proba2[85*256+80]
24=proba2[85*256+82]
24=proba2[85*256+83]
33=proba2[85*256+84]
50=proba2[85*256+85]
41=proba2[85*256+86]
54=proba2[85*256+87]
44=proba2[85*256+88]
39=proba2[85*256+90]
50=proba2[85*256+97]
61=proba2[85*256+98]
54=proba2[85*256+101]
61=proba2[85*256+102]
54=proba2[85*256+103]
61=proba2[85*256+104]
54=proba2[85*256+105]
61=proba2[85*256+106]
61=proba2[85*256+107]
47=proba2[85*256+108]
61=proba2[85*256+109]
36=proba2[85*256+110]
61=proba2[85*256+112]
50=proba2[85*256+114]
43=proba2[85*256+115]
61=proba2[85*256+116]
61=proba2[85*256+118]
50=proba2[85*256+120]
47=proba2[85*256+121]
50=proba2[85*256+122]
73=proba1[86]
55=proba2[86*256+33]
55=proba2[86*256+43]
55=proba2[86*256+46]
55=proba2[86*256+48]
31=proba2[86*256+49]
31=proba2[86*256+50]
37=proba2[86*256+51]
55=proba2[86*256+52]
48=proba2[86*256+53]
44=proba2[86*256+54]
48=proba2[86*256+55]
48=proba2[86*256+56]
48=proba2[86*256+57]
20=proba2[86*256+65]
44=proba2[86*256+66]
55=proba2[86*256+67]
35=proba2[86*256+68]
22=proba2[86*256+69]
48=proba2[86*256+70]
48=proba2[86*256+71]
48=proba2[86*256+72]
25=proba2[86*256+73]
44=proba2[86*256+74]
55=proba2[86*256+75]
44=proba2[86*256+76]
34=proba2[86*256+79]
44=proba2[86*256+80]
55=proba2[86*256+81]
41=proba2[86*256+82]
41=proba2[86*256+83]
38=proba2[86*256+84]
55=proba2[86*256+85]
44=proba2[86*256+86]
55=proba2[86*256+87]
55=proba2[86*256+89]
55=proba2[86*256+90]
26=proba2[86*256+97]
29=proba2[86*256+101]
44=proba2[86*256+102]
55=proba2[86*256+103]
55=proba2[86*256+104]
27=proba2[86*256+105]
48=proba2[86*256+107]
55=proba2[86*256+108]
48=proba2[86*256+109]
41=proba2[86*256+111]
48=proba2[86*256+112]
44=proba2[86*256+114]
55=proba2[86*256+117]
48=proba2[86*256+118]
55=proba2[86*256+122]
79=proba1[87]
51=proba2[87*256+33]
51=proba2[87*256+42]
40=proba2[87*256+46]
51=proba2[87*256+48]
35=proba2[87*256+49]
44=proba2[87*256+50]
35=proba2[87*256+51]
44=proba2[87*256+52]
51=proba2[87*256+53]
44=proba2[87*256+54]
37=proba2[87*256+55]
51=proba2[87*256+63]
22=proba2[87*256+65]
44=proba2[87*256+66]
40=proba2[87*256+67]
51=proba2[87*256+68]
33=proba2[87*256+69]
51=proba2[87*256+70]
51=proba2[87*256+71]
30=proba2[87*256+72]
31=proba2[87*256+73]
44=proba2[87*256+74]
40=proba2[87*256+75]
40=proba2[87*256+76]
37=proba2[87*256+77]
44=proba2[87*256+78]
33=proba2[87*256+79]
40=proba2[87*256+80]
35=proba2[87*256+81]
40=proba2[87*256+82]
40=proba2[87*256+83]
40=proba2[87*256+84]
51=proba2[87*256+85]
51=proba2[87*256+86]
44=proba2[87*256+87]
51=proba2[87*256+88]
44=proba2[87*256+90]
28=proba2[87*256+97]
51=proba2[87*256+100]
27=proba2[87*256+101]
40=proba2[87*256+104]
33=proba2[87*256+105]
51=proba2[87*256+106]
37=proba2[87*256+107]
51=proba2[87*256+110]
40=proba2[87*256+111]
37=proba2[87*256+113]
44=proba2[87*256+114]
51=proba2[87*256+115]
51=proba2[87*256+120]
80=proba1[88]
50=proba2[88*256+45]
50=proba2[88*256+46]
39=proba2[88*256+48]
30=proba2[88*256+49]
27=proba2[88*256+50]
43=proba2[88*256+51]
34=proba2[88*256+52]
50=proba2[88*256+53]
39=proba2[88*256+54]
34=proba2[88*256+55]
50=proba2[88*256+56]
50=proba2[88*256+57]
28=proba2[88*256+65]
50=proba2[88*256+66]
30=proba2[88*256+67]
32=proba2[88*256+68]
32=proba2[88*256+69]
36=proba2[88*256+70]
39=proba2[88*256+71]
34=proba2[88*256+73]
50=proba2[88*256+74]
50=proba2[88*256+75]
50=proba2[88*256+77]
43=proba2[88*256+78]
39=proba2[88*256+79]
34=proba2[88*256+80]
39=proba2[88*256+82]
39=proba2[88*256+83]
43=proba2[88*256+84]
50=proba2[88*256+85]
50=proba2[88*256+86]
39=proba2[88*256+87]
25=proba2[88*256+88]
39=proba2[88*256+89]
36=proba2[88*256+97]
50=proba2[88*256+99]
43=proba2[88*256+100]
43=proba2[88*256+101]
39=proba2[88*256+103]
50=proba2[88*256+104]
50=proba2[88*256+105]
50=proba2[88*256+106]
50=proba2[88*256+109]
50=proba2[88*256+111]
50=proba2[88*256+112]
50=proba2[88*256+113]
39=proba2[88*256+114]
50=proba2[88*256+116]
50=proba2[88*256+118]
39=proba2[88*256+119]
50=proba2[88*256+120]
50=proba2[88*256+121]
39=proba2[88*256+122]
78=proba1[89]
47=proba2[89*256+32]
54=proba2[89*256+38]
54=proba2[89*256+45]
43=proba2[89*256+48]
30=proba2[89*256+49]
43=proba2[89*256+50]
47=proba2[89*256+51]
47=proba2[89*256+52]
40=proba2[89*256+53]
36=proba2[89*256+54]
33=proba2[89*256+55]
54=proba2[89*256+56]
43=proba2[89*256+57]
28=proba2[89*256+65]
43=proba2[89*256+66]
35=proba2[89*256+67]
33=proba2[89*256+68]
38=proba2[89*256+69]
54=proba2[89*256+70]
28=proba2[89*256+71]
54=proba2[89*256+72]
43=proba2[89*256+73]
54=proba2[89*256+74]
47=proba2[89*256+75]
31=proba2[89*256+76]
31=proba2[89*256+77]
38=proba2[89*256+78]
28=proba2[89*256+79]
36=proba2[89*256+80]
40=proba2[89*256+81]
47=proba2[89*256+82]
31=proba2[89*256+83]
43=proba2[89*256+84]
47=proba2[89*256+85]
54=proba2[89*256+86]
54=proba2[89*256+87]
54=proba2[89*256+88]
54=proba2[89*256+89]
38=proba2[89*256+90]
36=proba2[89*256+97]
54=proba2[89*256+99]
47=proba2[89*256+100]
54=proba2[89*256+101]
38=proba2[89*256+103]
54=proba2[89*256+104]
47=proba2[89*256+105]
47=proba2[89*256+106]
43=proba2[89*256+107]
47=proba2[89*256+108]
54=proba2[89*256+110]
33=proba2[89*256+111]
47=proba2[89*256+112]
54=proba2[89*256+114]
47=proba2[89*256+115]
43=proba2[89*256+116]
54=proba2[89*256+117]
47=proba2[89*256+118]
35=proba2[89*256+119]
54=proba2[89*256+120]
47=proba2[89*256+122]
76=proba1[90]
54=proba2[90*256+32]
54=proba2[90*256+38]
54=proba2[90*256+42]
33=proba2[90*256+48]
34=proba2[90*256+49]
34=proba2[90*256+50]
47=proba2[90*256+51]
32=proba2[90*256+52]
40=proba2[90*256+53]
38=proba2[90*256+54]
40=proba2[90*256+55]
54=proba2[90*256+56]
54=proba2[90*256+57]
25=proba2[90*256+65]
38=proba2[90*256+66]
47=proba2[90*256+67]
47=proba2[90*256+68]
24=proba2[90*256+69]
38=proba2[90*256+70]
31=proba2[90*256+73]
43=proba2[90*256+74]
43=proba2[90*256+75]
47=proba2[90*256+76]
54=proba2[90*256+77]
25=proba2[90*256+79]
54=proba2[90*256+80]
47=proba2[90*256+81]
47=proba2[90*256+82]
43=proba2[90*256+83]
43=proba2[90*256+84]
43=proba2[90*256+85]
47=proba2[90*256+86]
47=proba2[90*256+87]
47=proba2[90*256+88]
47=proba2[90*256+89]
34=proba2[90*256+90]
36=proba2[90*256+97]
54=proba2[90*256+98]
54=proba2[90*256+99]
54=proba2[90*256+100]
34=proba2[90*256+101]
38=proba2[90*256+105]
54=proba2[90*256+106]
40=proba2[90*256+107]
47=proba2[90*256+109]
47=proba2[90*256+110]
32=proba2[90*256+111]
47=proba2[90*256+112]
47=proba2[90*256+115]
54=proba2[90*256+116]
33=proba2[90*256+117]
43=proba2[90*256+120]
54=proba2[90*256+121]
97=proba1[91]
23=proba2[91*256+48]
23=proba2[91*256+57]
17=proba2[91*256+77]
23=proba2[91*256+83]
23=proba2[91*256+93]
23=proba2[91*256+97]
17=proba2[91*256+98]
23=proba2[91*256+110]
23=proba2[91*256+117]
118=proba1[93]
12=proba2[93*256+48]
19=proba2[93*256+54]
19=proba2[93*256+59]
12=proba2[93*256+64]
19=proba2[93*256+125]
23=proba2[94*256+40]
23=proba2[94*256+41]
23=proba2[94*256+42]
23=proba2[94*256+49]
23=proba2[94*256+95]
23=proba2[94*256+98]
16=proba2[94*256+99]
23=proba2[94*256+109]
23=proba2[94*256+110]
98=proba1[95]
35=proba2[95*256+33]
49=proba2[95*256+41]
49=proba2[95*256+42]
49=proba2[95*256+43]
42=proba2[95*256+45]
33=proba2[95*256+48]
29=proba2[95*256+49]
31=proba2[95*256+50]
42=proba2[95*256+51]
42=proba2[95*256+52]
42=proba2[95*256+53]
35=proba2[95*256+54]
49=proba2[95*256+55]
49=proba2[95*256+56]
29=proba2[95*256+57]
49=proba2[95*256+64]
49=proba2[95*256+65]
49=proba2[95*256+68]
49=proba2[95*256+72]
49=proba2[95*256+77]
49=proba2[95*256+80]
49=proba2[95*256+85]
27=proba2[95*256+95]
35=proba2[95*256+97]
49=proba2[95*256+98]
33=proba2[95*256+99]
27=proba2[95*256+100]
38=proba2[95*256+101]
29=proba2[95*256+102]
49=proba2[95*256+103]
49=proba2[95*256+104]
49=proba2[95*256+105]
42=proba2[95*256+106]
42=proba2[95*256+107]
33=proba2[95*256+108]
49=proba2[95*256+109]
35=proba2[95*256+111]
42=proba2[95*256+112]
35=proba2[95*256+114]
38=proba2[95*256+115]
31=proba2[95*256+116]
49=proba2[95*256+117]
38=proba2[95*256+118]
42=proba2[95*256+119]
42=proba2[95*256+120]
42=proba2[95*256+121]
42=proba2[95*256+122]
49=proba2[95*256+126]
118=proba1[96]
10=proba2[96*256+49]
10=proba2[96*256+51]
10=proba2[96*256+100]
27=proba1[97]
85=proba2[97*256+32]
83=proba2[97*256+33]
112=proba2[97*256+34]
98=proba2[97*256+35]
98=proba2[97*256+36]
105=proba2[97*256+37]
86=proba2[97*256+38]
112=proba2[97*256+39]
105=proba2[97*256+40]
105=proba2[97*256+41]
98=proba2[97*256+42]
98=proba2[97*256+43]
98=proba2[97*256+44]
83=proba2[97*256+45]
84=proba2[97*256+46]
57=proba2[97*256+48]
46=proba2[97*256+49]
51=proba2[97*256+50]
59=proba2[97*256+51]
61=proba2[97*256+52]
62=proba2[97*256+53]
60=proba2[97*256+54]
58=proba2[97*256+55]
64=proba2[97*256+56]
55=proba2[97*256+57]
98=proba2[97*256+59]
112=proba2[97*256+60]
98=proba2[97*256+61]
101=proba2[97*256+62]
105=proba2[97*256+63]
105=proba2[97*256+64]
112=proba2[97*256+65]
98=proba2[97*256+66]
92=proba2[97*256+67]
105=proba2[97*256+68]
98=proba2[97*256+70]
98=proba2[97*256+71]
105=proba2[97*256+72]
112=proba2[97*256+73]
105=proba2[97*256+74]
92=proba2[97*256+75]
101=proba2[97*256+76]
88=proba2[97*256+77]
98=proba2[97*256+78]
105=proba2[97*256+79]
85=proba2[97*256+80]
105=proba2[97*256+81]
91=proba2[97*256+82]
94=proba2[97*256+83]
98=proba2[97*256+84]
101=proba2[97*256+86]
112=proba2[97*256+87]
112=proba2[97*256+88]
112=proba2[97*256+90]
112=proba2[97*256+93]
83=proba2[97*256+95]
44=proba2[97*256+97]
32=proba2[97*256+98]
31=proba2[97*256+99]
32=proba2[97*256+100]
45=proba2[97*256+101]
42=proba2[97*256+102]
34=proba2[97*256+103]
45=proba2[97*256+104]
32=proba2[97*256+105]
52=proba2[97*256+106]
41=proba2[97*256+107]
23=proba2[97*256+108]
28=proba2[97*256+109]
18=proba2[97*256+110]
49=proba2[97*256+111]
37=proba2[97*256+112]
57=proba2[97*256+113]
20=proba2[97*256+114]
28=proba2[97*256+115]
27=proba2[97*256+116]
30=proba2[97*256+117]
38=proba2[97*256+118]
52=proba2[97*256+119]
46=proba2[97*256+120]
41=proba2[97*256+121]
40=proba2[97*256+122]
28=proba1[98]
86=proba2[98*256+32]
89=proba2[98*256+33]
99=proba2[98*256+36]
93=proba2[98*256+38]
99=proba2[98*256+40]
99=proba2[98*256+41]
99=proba2[98*256+42]
93=proba2[98*256+43]
99=proba2[98*256+44]
89=proba2[98*256+45]
80=proba2[98*256+46]
52=proba2[98*256+48]
46=proba2[98*256+49]
48=proba2[98*256+50]
54=proba2[98*256+51]
59=proba2[98*256+52]
59=proba2[98*256+53]
56=proba2[98*256+54]
55=proba2[98*256+55]
59=proba2[98*256+56]
55=proba2[98*256+57]
99=proba2[98*256+59]
93=proba2[98*256+61]
99=proba2[98*256+63]
93=proba2[98*256+64]
83=proba2[98*256+65]
93=proba2[98*256+66]
99=proba2[98*256+67]
89=proba2[98*256+68]
99=proba2[98*256+69]
93=proba2[98*256+70]
83=proba2[98*256+71]
86=proba2[98*256+73]
93=proba2[98*256+74]
99=proba2[98*256+75]
93=proba2[98*256+78]
86=proba2[98*256+82]
93=proba2[98*256+83]
93=proba2[98*256+84]
99=proba2[98*256+90]
86=proba2[98*256+95]
18=proba2[98*256+97]
37=proba2[98*256+98]
40=proba2[98*256+99]
41=proba2[98*256+100]
16=proba2[98*256+101]
49=proba2[98*256+102]
49=proba2[98*256+103]
53=proba2[98*256+104]
22=proba2[98*256+105]
51=proba2[98*256+106]
56=proba2[98*256+107]
28=proba2[98*256+108]
45=proba2[98*256+109]
50=proba2[98*256+110]
18=proba2[98*256+111]
50=proba2[98*256+112]
63=proba2[98*256+113]
25=proba2[98*256+114]
40=proba2[98*256+115]
49=proba2[98*256+116]
31=proba2[98*256+117]
54=proba2[98*256+118]
56=proba2[98*256+119]
60=proba2[98*256+120]
40=proba2[98*256+121]
53=proba2[98*256+122]
25=proba1[99]
87=proba2[99*256+32]
81=proba2[99*256+33]
103=proba2[99*256+34]
92=proba2[99*256+36]
103=proba2[99*256+37]
85=proba2[99*256+38]
96=proba2[99*256+39]
89=proba2[99*256+42]
89=proba2[99*256+43]
92=proba2[99*256+44]
85=proba2[99*256+45]
89=proba2[99*256+46]
55=proba2[99*256+48]
45=proba2[99*256+49]
49=proba2[99*256+50]
56=proba2[99*256+51]
60=proba2[99*256+52]
62=proba2[99*256+53]
57=proba2[99*256+54]
60=proba2[99*256+55]
65=proba2[99*256+56]
56=proba2[99*256+57]
96=proba2[99*256+59]
85=proba2[99*256+61]
89=proba2[99*256+64]
89=proba2[99*256+65]
103=proba2[99*256+66]
96=proba2[99*256+67]
103=proba2[99*256+68]
92=proba2[99*256+69]
87=proba2[99*256+70]
87=proba2[99*256+72]
103=proba2[99*256+74]
103=proba2[99*256+75]
103=proba2[99*256+76]
103=proba2[99*256+80]
92=proba2[99*256+81]
103=proba2[99*256+82]
92=proba2[99*256+83]
96=proba2[99*256+84]
92=proba2[99*256+85]
103=proba2[99*256+86]
103=proba2[99*256+88]
87=proba2[99*256+89]
92=proba2[99*256+95]
19=proba2[99*256+97]
44=proba2[99*256+98]
39=proba2[99*256+99]
43=proba2[99*256+100]
24=proba2[99*256+101]
49=proba2[99*256+102]
48=proba2[99*256+103]
16=proba2[99*256+104]
31=proba2[99*256+105]
53=proba2[99*256+106]
28=proba2[99*256+107]
30=proba2[99*256+108]
43=proba2[99*256+109]
50=proba2[99*256+110]
18=proba2[99*256+111]
45=proba2[99*256+112]
50=proba2[99*256+113]
31=proba2[99*256+114]
43=proba2[99*256+115]
37=proba2[99*256+116]
39=proba2[99*256+117]
48=proba2[99*256+118]
58=proba2[99*256+119]
62=proba2[99*256+120]
38=proba2[99*256+121]
62=proba2[99*256+122]
103=proba2[99*256+124]
30=proba1[100]
100=proba2[100*256+32]
76=proba2[100*256+33]
86=proba2[100*256+35]
100=proba2[100*256+37]
86=proba2[100*256+38]
84=proba2[100*256+39]
86=proba2[100*256+42]
100=proba2[100*256+43]
79=proba2[100*256+45]
84=proba2[100*256+46]
49=proba2[100*256+48]
43=proba2[100*256+49]
47=proba2[100*256+50]
53=proba2[100*256+51]
55=proba2[100*256+52]
57=proba2[100*256+53]
54=proba2[100*256+54]
54=proba2[100*256+55]
61=proba2[100*256+56]
53=proba2[100*256+57]
89=proba2[100*256+59]
100=proba2[100*256+61]
100=proba2[100*256+64]
93=proba2[100*256+65]
93=proba2[100*256+66]
100=proba2[100*256+68]
84=proba2[100*256+69]
100=proba2[100*256+70]
93=proba2[100*256+71]
93=proba2[100*256+72]
100=proba2[100*256+73]
100=proba2[100*256+76]
84=proba2[100*256+77]
93=proba2[100*256+78]
100=proba2[100*256+79]
89=proba2[100*256+80]
100=proba2[100*256+81]
100=proba2[100*256+82]
93=proba2[100*256+83]
100=proba2[100*256+84]
93=proba2[100*256+85]
100=proba2[100*256+86]
93=proba2[100*256+87]
100=proba2[100*256+89]
89=proba2[100*256+90]
86=proba2[100*256+95]
20=proba2[100*256+97]
41=proba2[100*256+98]
41=proba2[100*256+99]
38=proba2[100*256+100]
15=proba2[100*256+101]
43=proba2[100*256+102]
43=proba2[100*256+103]
46=proba2[100*256+104]
21=proba2[100*256+105]
36=proba2[100*256+106]
52=proba2[100*256+107]
41=proba2[100*256+108]
40=proba2[100*256+109]
48=proba2[100*256+110]
21=proba2[100*256+111]
45=proba2[100*256+112]
64=proba2[100*256+113]
26=proba2[100*256+114]
38=proba2[100*256+115]
47=proba2[100*256+116]
29=proba2[100*256+117]
49=proba2[100*256+118]
50=proba2[100*256+119]
57=proba2[100*256+120]
37=proba2[100*256+121]
53=proba2[100*256+122]
36=proba1[101]
75=proba2[101*256+32]
82=proba2[101*256+33]
91=proba2[101*256+35]
91=proba2[101*256+36]
110=proba2[101*256+37]
89=proba2[101*256+38]
103=proba2[101*256+39]
103=proba2[101*256+40]
103=proba2[101*256+41]
84=proba2[101*256+42]
92=proba2[101*256+43]
96=proba2[101*256+44]
78=proba2[101*256+45]
87=proba2[101*256+46]
54=proba2[101*256+48]
42=proba2[101*256+49]
47=proba2[101*256+50]
54=proba2[101*256+51]
58=proba2[101*256+52]
58=proba2[101*256+53]
56=proba2[101*256+54]
54=proba2[101*256+55]
61=proba2[101*256+56]
52=proba2[101*256+57]
103=proba2[101*256+59]
92=proba2[101*256+61]
103=proba2[101*256+63]
91=proba2[101*256+64]
103=proba2[101*256+65]
92=proba2[101*256+66]
92=proba2[101*256+67]
89=proba2[101*256+68]
110=proba2[101*256+69]
99=proba2[101*256+70]
99=proba2[101*256+71]
99=proba2[101*256+72]
110=proba2[101*256+73]
103=proba2[101*256+74]
92=proba2[101*256+75]
91=proba2[101*256+76]
92=proba2[101*256+77]
96=proba2[101*256+78]
110=proba2[101*256+79]
110=proba2[101*256+80]
103=proba2[101*256+81]
87=proba2[101*256+82]
87=proba2[101*256+83]
94=proba2[101*256+84]
92=proba2[101*256+85]
103=proba2[101*256+86]
99=proba2[101*256+87]
96=proba2[101*256+88]
110=proba2[101*256+89]
94=proba2[101*256+90]
110=proba2[101*256+93]
103=proba2[101*256+94]
83=proba2[101*256+95]
32=proba2[101*256+97]
33=proba2[101*256+98]
33=proba2[101*256+99]
32=proba2[101*256+100]
38=proba2[101*256+101]
41=proba2[101*256+102]
38=proba2[101*256+103]
51=proba2[101*256+104]
40=proba2[101*256+105]
53=proba2[101*256+106]
47=proba2[101*256+107]
22=proba2[101*256+108]
33=proba2[101*256+109]
23=proba2[101*256+110]
44=proba2[101*256+111]
38=proba2[101*256+112]
62=proba2[101*256+113]
16=proba2[101*256+114]
25=proba2[101*256+115]
26=proba2[101*256+116]
35=proba2[101*256+117]
39=proba2[101*256+118]
49=proba2[101*256+119]
41=proba2[101*256+120]
43=proba2[101*256+121]
45=proba2[101*256+122]
110=proba2[101*256+126]
31=proba1[102]
84=proba2[102*256+32]
74=proba2[102*256+33]
88=proba2[102*256+38]
88=proba2[102*256+41]
95=proba2[102*256+42]
84=proba2[102*256+44]
84=proba2[102*256+45]
81=proba2[102*256+46]
52=proba2[102*256+48]
41=proba2[102*256+49]
46=proba2[102*256+50]
54=proba2[102*256+51]
53=proba2[102*256+52]
52=proba2[102*256+53]
53=proba2[102*256+54]
53=proba2[102*256+55]
57=proba2[102*256+56]
52=proba2[102*256+57]
88=proba2[102*256+64]
84=proba2[102*256+65]
84=proba2[102*256+66]
88=proba2[102*256+67]
88=proba2[102*256+68]
95=proba2[102*256+69]
95=proba2[102*256+70]
88=proba2[102*256+72]
95=proba2[102*256+73]
95=proba2[102*256+74]
95=proba2[102*256+75]
95=proba2[102*256+76]
81=proba2[102*256+78]
95=proba2[102*256+80]
95=proba2[102*256+84]
95=proba2[102*256+85]
88=proba2[102*256+86]
95=proba2[102*256+87]
88=proba2[102*256+90]
81=proba2[102*256+95]
20=proba2[102*256+97]
41=proba2[102*256+98]
40=proba2[102*256+99]
42=proba2[102*256+100]
25=proba2[102*256+101]
28=proba2[102*256+102]
40=proba2[102*256+103]
52=proba2[102*256+104]
24=proba2[102*256+105]
50=proba2[102*256+106]
51=proba2[102*256+107]
26=proba2[102*256+108]
40=proba2[102*256+109]
50=proba2[102*256+110]
22=proba2[102*256+111]
43=proba2[102*256+112]
64=proba2[102*256+113]
17=proba2[102*256+114]
40=proba2[102*256+115]
35=proba2[102*256+116]
31=proba2[102*256+117]
51=proba2[102*256+118]
54=proba2[102*256+119]
48=proba2[102*256+120]
48=proba2[102*256+121]
59=proba2[102*256+122]
32=proba1[103]
86=proba2[103*256+32]
81=proba2[103*256+33]
97=proba2[103*256+35]
90=proba2[103*256+36]
97=proba2[103*256+38]
83=proba2[103*256+39]
97=proba2[103*256+42]
90=proba2[103*256+43]
83=proba2[103*256+45]
90=proba2[103*256+46]
55=proba2[103*256+48]
46=proba2[103*256+49]
49=proba2[103*256+50]
56=proba2[103*256+51]
59=proba2[103*256+52]
57=proba2[103*256+53]
56=proba2[103*256+54]
56=proba2[103*256+55]
61=proba2[103*256+56]
56=proba2[103*256+57]
97=proba2[103*256+59]
97=proba2[103*256+60]
86=proba2[103*256+64]
81=proba2[103*256+65]
83=proba2[103*256+66]
97=proba2[103*256+67]
97=proba2[103*256+68]
86=proba2[103*256+69]
97=proba2[103*256+70]
97=proba2[103*256+71]
90=proba2[103*256+72]
97=proba2[103*256+74]
97=proba2[103*256+75]
97=proba2[103*256+76]
90=proba2[103*256+78]
90=proba2[103*256+79]
83=proba2[103*256+80]
97=proba2[103*256+84]
97=proba2[103*256+86]
83=proba2[103*256+87]
90=proba2[103*256+90]
97=proba2[103*256+94]
86=proba2[103*256+95]
18=proba2[103*256+97]
41=proba2[103*256+98]
46=proba2[103*256+99]
43=proba2[103*256+100]
18=proba2[103*256+101]
48=proba2[103*256+102]
38=proba2[103*256+103]
35=proba2[103*256+104]
24=proba2[103*256+105]
55=proba2[103*256+106]
54=proba2[103*256+107]
32=proba2[103*256+108]
42=proba2[103*256+109]
32=proba2[103*256+110]
21=proba2[103*256+111]
45=proba2[103*256+112]
67=proba2[103*256+113]
25=proba2[103*256+114]
41=proba2[103*256+115]
45=proba2[103*256+116]
23=proba2[103*256+117]
55=proba2[103*256+118]
47=proba2[103*256+119]
61=proba2[103*256+120]
43=proba2[103*256+121]
57=proba2[103*256+122]
38=proba1[104]
79=proba2[104*256+32]
90=proba2[104*256+33]
97=proba2[104*256+39]
97=proba2[104*256+42]
90=proba2[104*256+45]
84=proba2[104*256+46]
53=proba2[104*256+48]
47=proba2[104*256+49]
49=proba2[104*256+50]
56=proba2[104*256+51]
58=proba2[104*256+52]
59=proba2[104*256+53]
56=proba2[104*256+54]
55=proba2[104*256+55]
60=proba2[104*256+56]
55=proba2[104*256+57]
97=proba2[104*256+59]
97=proba2[104*256+60]
97=proba2[104*256+61]
86=proba2[104*256+64]
86=proba2[104*256+65]
86=proba2[104*256+66]
90=proba2[104*256+67]
97=proba2[104*256+68]
81=proba2[104*256+71]
97=proba2[104*256+74]
97=proba2[104*256+75]
97=proba2[104*256+79]
97=proba2[104*256+80]
97=proba2[104*256+81]
97=proba2[104*256+82]
86=proba2[104*256+83]
81=proba2[104*256+84]
97=proba2[104*256+87]
86=proba2[104*256+88]
84=proba2[104*256+89]
90=proba2[104*256+95]
14=proba2[104*256+97]
45=proba2[104*256+98]
47=proba2[104*256+99]
47=proba2[104*256+100]
16=proba2[104*256+101]
54=proba2[104*256+102]
53=proba2[104*256+103]
54=proba2[104*256+104]
19=proba2[104*256+105]
56=proba2[104*256+106]
54=proba2[104*256+107]
41=proba2[104*256+108]
42=proba2[104*256+109]
41=proba2[104*256+110]
20=proba2[104*256+111]
47=proba2[104*256+112]
59=proba2[104*256+113]
32=proba2[104*256+114]
48=proba2[104*256+115]
39=proba2[104*256+116]
31=proba2[104*256+117]
58=proba2[104*256+118]
56=proba2[104*256+119]
57=proba2[104*256+120]
39=proba2[104*256+121]
59=proba2[104*256+122]
42=proba1[105]
95=proba2[105*256+32]
90=proba2[105*256+33]
108=proba2[105*256+36]
97=proba2[105*256+38]
108=proba2[105*256+39]
108=proba2[105*256+41]
95=proba2[105*256+42]
92=proba2[105*256+43]
108=proba2[105*256+44]
81=proba2[105*256+45]
88=proba2[105*256+46]
59=proba2[105*256+48]
50=proba2[105*256+49]
54=proba2[105*256+50]
61=proba2[105*256+51]
65=proba2[105*256+52]
64=proba2[105*256+53]
62=proba2[105*256+54]
60=proba2[105*256+55]
66=proba2[105*256+56]
58=proba2[105*256+57]
101=proba2[105*256+59]
101=proba2[105*256+61]
101=proba2[105*256+63]
97=proba2[105*256+64]
95=proba2[105*256+65]
101=proba2[105*256+66]
97=proba2[105*256+67]
97=proba2[105*256+68]
97=proba2[105*256+69]
108=proba2[105*256+70]
90=proba2[105*256+71]
101=proba2[105*256+75]
92=proba2[105*256+76]
95=proba2[105*256+77]
90=proba2[105*256+78]
108=proba2[105*256+79]
95=proba2[105*256+80]
95=proba2[105*256+82]
108=proba2[105*256+83]
92=proba2[105*256+84]
108=proba2[105*256+86]
108=proba2[105*256+89]
108=proba2[105*256+90]
90=proba2[105*256+95]
30=proba2[105*256+97]
38=proba2[105*256+98]
24=proba2[105*256+99]
33=proba2[105*256+100]
22=proba2[105*256+101]
43=proba2[105*256+102]
35=proba2[105*256+103]
57=proba2[105*256+104]
54=proba2[105*256+105]
56=proba2[105*256+106]
38=proba2[105*256+107]
24=proba2[105*256+108]
32=proba2[105*256+109]
18=proba2[105*256+110]
32=proba2[105*256+111]
39=proba2[105*256+112]
50=proba2[105*256+113]
30=proba2[105*256+114]
24=proba2[105*256+115]
28=proba2[105*256+116]
50=proba2[105*256+117]
39=proba2[105*256+118]
59=proba2[105*256+119]
45=proba2[105*256+120]
65=proba2[105*256+121]
44=proba2[105*256+122]
108=proba2[105*256+123]
108=proba2[105*256+124]
32=proba1[106]
91=proba2[106*256+33]
91=proba2[106*256+36]
84=proba2[106*256+38]
84=proba2[106*256+39]
91=proba2[106*256+43]
80=proba2[106*256+44]
80=proba2[106*256+45]
80=proba2[106*256+46]
62=proba2[106*256+48]
48=proba2[106*256+49]
50=proba2[106*256+50]
57=proba2[106*256+51]
56=proba2[106*256+52]
58=proba2[106*256+53]
57=proba2[106*256+54]
56=proba2[106*256+55]
62=proba2[106*256+56]
57=proba2[106*256+57]
84=proba2[106*256+63]
84=proba2[106*256+64]
84=proba2[106*256+65]
91=proba2[106*256+68]
91=proba2[106*256+69]
91=proba2[106*256+70]
84=proba2[106*256+75]
91=proba2[106*256+77]
91=proba2[106*256+80]
80=proba2[106*256+81]
84=proba2[106*256+85]
84=proba2[106*256+87]
91=proba2[106*256+89]
84=proba2[106*256+95]
19=proba2[106*256+97]
35=proba2[106*256+98]
31=proba2[106*256+99]
38=proba2[106*256+100]
19=proba2[106*256+101]
36=proba2[106*256+102]
43=proba2[106*256+103]
48=proba2[106*256+104]
30=proba2[106*256+105]
39=proba2[106*256+106]
44=proba2[106*256+107]
35=proba2[106*256+108]
32=proba2[106*256+109]
46=proba2[106*256+110]
18=proba2[106*256+111]
30=proba2[106*256+112]
64=proba2[106*256+113]
41=proba2[106*256+114]
40=proba2[106*256+115]
44=proba2[106*256+116]
22=proba2[106*256+117]
48=proba2[106*256+118]
56=proba2[106*256+119]
61=proba2[106*256+120]
47=proba2[106*256+121]
59=proba2[106*256+122]
91=proba2[106*256+123]
37=proba1[107]
78=proba2[107*256+32]
78=proba2[107*256+33]
92=proba2[107*256+34]
92=proba2[107*256+36]
76=proba2[107*256+38]
92=proba2[107*256+39]
92=proba2[107*256+41]
92=proba2[107*256+42]
92=proba2[107*256+44]
76=proba2[107*256+45]
76=proba2[107*256+46]
50=proba2[107*256+48]
41=proba2[107*256+49]
45=proba2[107*256+50]
52=proba2[107*256+51]
55=proba2[107*256+52]
57=proba2[107*256+53]
51=proba2[107*256+54]
50=proba2[107*256+55]
50=proba2[107*256+56]
49=proba2[107*256+57]
92=proba2[107*256+59]
92=proba2[107*256+63]
81=proba2[107*256+64]
85=proba2[107*256+65]
92=proba2[107*256+68]
92=proba2[107*256+69]
92=proba2[107*256+70]
78=proba2[107*256+71]
92=proba2[107*256+72]
92=proba2[107*256+77]
92=proba2[107*256+78]
74=proba2[107*256+80]
85=proba2[107*256+81]
92=proba2[107*256+82]
81=proba2[107*256+84]
85=proba2[107*256+85]
92=proba2[107*256+87]
92=proba2[107*256+88]
92=proba2[107*256+95]
16=proba2[107*256+97]
45=proba2[107*256+98]
44=proba2[107*256+99]
46=proba2[107*256+100]
19=proba2[107*256+101]
47=proba2[107*256+102]
50=proba2[107*256+103]
35=proba2[107*256+104]
18=proba2[107*256+105]
50=proba2[107*256+106]
39=proba2[107*256+107]
36=proba2[107*256+108]
41=proba2[107*256+109]
43=proba2[107*256+110]
23=proba2[107*256+111]
46=proba2[107*256+112]
66=proba2[107*256+113]
32=proba2[107*256+114]
36=proba2[107*256+115]
44=proba2[107*256+116]
34=proba2[107*256+117]
58=proba2[107*256+118]
46=proba2[107*256+119]
63=proba2[107*256+120]
30=proba2[107*256+121]
50=proba2[107*256+122]
30=proba1[108]
92=proba2[108*256+32]
85=proba2[108*256+33]
106=proba2[108*256+35]
95=proba2[108*256+36]
83=proba2[108*256+38]
106=proba2[108*256+39]
106=proba2[108*256+40]
106=proba2[108*256+41]
95=proba2[108*256+42]
106=proba2[108*256+43]
86=proba2[108*256+44]
83=proba2[108*256+45]
83=proba2[108*256+46]
53=proba2[108*256+48]
47=proba2[108*256+49]
52=proba2[108*256+50]
57=proba2[108*256+51]
63=proba2[108*256+52]
62=proba2[108*256+53]
59=proba2[108*256+54]
60=proba2[108*256+55]
63=proba2[108*256+56]
55=proba2[108*256+57]
95=proba2[108*256+59]
106=proba2[108*256+61]
86=proba2[108*256+64]
86=proba2[108*256+65]
90=proba2[108*256+66]
95=proba2[108*256+67]
99=proba2[108*256+68]
88=proba2[108*256+69]
106=proba2[108*256+70]
99=proba2[108*256+71]
99=proba2[108*256+75]
106=proba2[108*256+76]
95=proba2[108*256+77]
106=proba2[108*256+78]
99=proba2[108*256+79]
92=proba2[108*256+80]
99=proba2[108*256+82]
90=proba2[108*256+84]
92=proba2[108*256+85]
106=proba2[108*256+86]
106=proba2[108*256+87]
106=proba2[108*256+88]
99=proba2[108*256+89]
106=proba2[108*256+90]
86=proba2[108*256+95]
18=proba2[108*256+97]
42=proba2[108*256+98]
43=proba2[108*256+99]
36=proba2[108*256+100]
16=proba2[108*256+101]
43=proba2[108*256+102]
47=proba2[108*256+103]
50=proba2[108*256+104]
19=proba2[108*256+105]
56=proba2[108*256+106]
49=proba2[108*256+107]
22=proba2[108*256+108]
41=proba2[108*256+109]
53=proba2[108*256+110]
20=proba2[108*256+111]
41=proba2[108*256+112]
74=proba2[108*256+113]
50=proba2[108*256+114]
41=proba2[108*256+115]
39=proba2[108*256+116]
31=proba2[108*256+117]
44=proba2[108*256+118]
59=proba2[108*256+119]
63=proba2[108*256+120]
37=proba2[108*256+121]
60=proba2[108*256+122]
25=proba1[109]
83=proba2[109*256+32]
84=proba2[109*256+33]
102=proba2[109*256+34]
102=proba2[109*256+35]
91=proba2[109*256+36]
102=proba2[109*256+37]
88=proba2[109*256+38]
88=proba2[109*256+39]
95=proba2[109*256+41]
86=proba2[109*256+42]
88=proba2[109*256+43]
88=proba2[109*256+44]
83=proba2[109*256+45]
79=proba2[109*256+46]
54=proba2[109*256+48]
45=proba2[109*256+49]
49=proba2[109*256+50]
57=proba2[109*256+51]
60=proba2[109*256+52]
59=proba2[109*256+53]
60=proba2[109*256+54]
58=proba2[109*256+55]
61=proba2[109*256+56]
54=proba2[109*256+57]
91=proba2[109*256+59]
102=proba2[109*256+60]
91=proba2[109*256+61]
95=proba2[109*256+63]
81=proba2[109*256+64]
88=proba2[109*256+65]
102=proba2[109*256+66]
102=proba2[109*256+67]
95=proba2[109*256+68]
102=proba2[109*256+70]
95=proba2[109*256+71]
91=proba2[109*256+72]
102=proba2[109*256+74]
102=proba2[109*256+76]
91=proba2[109*256+77]
102=proba2[109*256+78]
102=proba2[109*256+80]
102=proba2[109*256+81]
95=proba2[109*256+84]
95=proba2[109*256+85]
95=proba2[109*256+86]
102=proba2[109*256+88]
102=proba2[109*256+89]
91=proba2[109*256+95]
12=proba2[109*256+97]
34=proba2[109*256+98]
41=proba2[109*256+99]
45=proba2[109*256+100]
20=proba2[109*256+101]
50=proba2[109*256+102]
49=proba2[109*256+103]
53=proba2[109*256+104]
20=proba2[109*256+105]
51=proba2[109*256+106]
55=proba2[109*256+107]
44=proba2[109*256+108]
35=proba2[109*256+109]
50=proba2[109*256+110]
20=proba2[109*256+111]
33=proba2[109*256+112]
67=proba2[109*256+113]
46=proba2[109*256+114]
39=proba2[109*256+115]
47=proba2[109*256+116]
34=proba2[109*256+117]
57=proba2[109*256+118]
60=proba2[109*256+119]
60=proba2[109*256+120]
35=proba2[109*256+121]
59=proba2[109*256+122]
102=proba2[109*256+125]
35=proba1[110]
86=proba2[110*256+32]
84=proba2[110*256+33]
94=proba2[110*256+35]
94=proba2[110*256+36]
105=proba2[110*256+37]
84=proba2[110*256+38]
91=proba2[110*256+39]
105=proba2[110*256+40]
105=proba2[110*256+41]
79=proba2[110*256+42]
87=proba2[110*256+43]
87=proba2[110*256+44]
77=proba2[110*256+45]
87=proba2[110*256+46]
51=proba2[110*256+48]
42=proba2[110*256+49]
47=proba2[110*256+50]
56=proba2[110*256+51]
61=proba2[110*256+52]
60=proba2[110*256+53]
54=proba2[110*256+54]
56=proba2[110*256+55]
62=proba2[110*256+56]
55=proba2[110*256+57]
91=proba2[110*256+59]
105=proba2[110*256+61]
98=proba2[110*256+63]
86=proba2[110*256+64]
86=proba2[110*256+65]
98=proba2[110*256+66]
105=proba2[110*256+67]
91=proba2[110*256+68]
98=proba2[110*256+69]
105=proba2[110*256+70]
89=proba2[110*256+71]
105=proba2[110*256+72]
84=proba2[110*256+73]
98=proba2[110*256+74]
105=proba2[110*256+75]
98=proba2[110*256+76]
98=proba2[110*256+77]
91=proba2[110*256+79]
98=proba2[110*256+80]
98=proba2[110*256+82]
83=proba2[110*256+83]
105=proba2[110*256+84]
105=proba2[110*256+85]
105=proba2[110*256+87]
89=proba2[110*256+89]
94=proba2[110*256+90]
105=proba2[110*256+94]
84=proba2[110*256+95]
22=proba2[110*256+97]
45=proba2[110*256+98]
30=proba2[110*256+99]
26=proba2[110*256+100]
17=proba2[110*256+101]
43=proba2[110*256+102]
29=proba2[110*256+103]
50=proba2[110*256+104]
22=proba2[110*256+105]
45=proba2[110*256+106]
41=proba2[110*256+107]
48=proba2[110*256+108]
48=proba2[110*256+109]
30=proba2[110*256+110]
24=proba2[110*256+111]
48=proba2[110*256+112]
64=proba2[110*256+113]
48=proba2[110*256+114]
33=proba2[110*256+115]
25=proba2[110*256+116]
39=proba2[110*256+117]
54=proba2[110*256+118]
58=proba2[110*256+119]
60=proba2[110*256+120]
40=proba2[110*256+121]
47=proba2[110*256+122]
105=proba2[110*256+125]
41=proba1[111]
90=proba2[111*256+32]
85=proba2[111*256+33]
108=proba2[111*256+35]
97=proba2[111*256+36]
108=proba2[111*256+37]
101=proba2[111*256+38]
101=proba2[111*256+39]
108=proba2[111*256+40]
89=proba2[111*256+42]
90=proba2[111*256+43]
94=proba2[111*256+44]
89=proba2[111*256+45]
83=proba2[111*256+46]
55=proba2[111*256+48]
45=proba2[111*256+49]
50=proba2[111*256+50]
57=proba2[111*256+51]
61=proba2[111*256+52]
62=proba2[111*256+53]
59=proba2[111*256+54]
56=proba2[111*256+55]
64=proba2[111*256+56]
55=proba2[111*256+57]
94=proba2[111*256+59]
97=proba2[111*256+61]
108=proba2[111*256+62]
108=proba2[111*256+63]
97=proba2[111*256+64]
92=proba2[111*256+66]
94=proba2[111*256+67]
108=proba2[111*256+68]
108=proba2[111*256+69]
94=proba2[111*256+70]
94=proba2[111*256+71]
108=proba2[111*256+72]
101=proba2[111*256+73]
97=proba2[111*256+74]
101=proba2[111*256+76]
97=proba2[111*256+77]
101=proba2[111*256+78]
94=proba2[111*256+80]
101=proba2[111*256+81]
92=proba2[111*256+82]
89=proba2[111*256+83]
89=proba2[111*256+84]
92=proba2[111*256+85]
101=proba2[111*256+88]
108=proba2[111*256+90]
92=proba2[111*256+95]
46=proba2[111*256+97]
36=proba2[111*256+98]
34=proba2[111*256+99]
36=proba2[111*256+100]
44=proba2[111*256+101]
41=proba2[111*256+102]
39=proba2[111*256+103]
47=proba2[111*256+104]
33=proba2[111*256+105]
53=proba2[111*256+106]
42=proba2[111*256+107]
25=proba2[111*256+108]
29=proba2[111*256+109]
19=proba2[111*256+110]
33=proba2[111*256+111]
34=proba2[111*256+112]
60=proba2[111*256+113]
23=proba2[111*256+114]
31=proba2[111*256+115]
30=proba2[111*256+116]
17=proba2[111*256+117]
42=proba2[111*256+118]
43=proba2[111*256+119]
48=proba2[111*256+120]
42=proba2[111*256+121]
48=proba2[111*256+122]
28=proba1[112]
92=proba2[112*256+32]
76=proba2[112*256+33]
85=proba2[112*256+36]
88=proba2[112*256+38]
85=proba2[112*256+43]
88=proba2[112*256+44]
85=proba2[112*256+45]
78=proba2[112*256+46]
55=proba2[112*256+48]
47=proba2[112*256+49]
49=proba2[112*256+50]
51=proba2[112*256+51]
58=proba2[112*256+52]
58=proba2[112*256+53]
58=proba2[112*256+54]
56=proba2[112*256+55]
65=proba2[112*256+56]
55=proba2[112*256+57]
92=proba2[112*256+59]
99=proba2[112*256+61]
99=proba2[112*256+63]
92=proba2[112*256+64]
85=proba2[112*256+67]
99=proba2[112*256+68]
99=proba2[112*256+69]
99=proba2[112*256+71]
92=proba2[112*256+72]
92=proba2[112*256+73]
99=proba2[112*256+75]
99=proba2[112*256+76]
88=proba2[112*256+77]
85=proba2[112*256+78]
92=proba2[112*256+79]
88=proba2[112*256+80]
88=proba2[112*256+82]
92=proba2[112*256+83]
83=proba2[112*256+84]
99=proba2[112*256+86]
99=proba2[112*256+87]
85=proba2[112*256+89]
99=proba2[112*256+90]
83=proba2[112*256+95]
17=proba2[112*256+97]
45=proba2[112*256+98]
40=proba2[112*256+99]
48=proba2[112*256+100]
20=proba2[112*256+101]
47=proba2[112*256+102]
48=proba2[112*256+103]
23=proba2[112*256+104]
21=proba2[112*256+105]
51=proba2[112*256+106]
53=proba2[112*256+107]
31=proba2[112*256+108]
42=proba2[112*256+109]
52=proba2[112*256+110]
21=proba2[112*256+111]
34=proba2[112*256+112]
60=proba2[112*256+113]
28=proba2[112*256+114]
33=proba2[112*256+115]
38=proba2[112*256+116]
36=proba2[112*256+117]
52=proba2[112*256+118]
54=proba2[112*256+119]
63=proba2[112*256+120]
39=proba2[112*256+121]
58=proba2[112*256+122]
54=proba1[113]
77=proba2[113*256+32]
77=proba2[113*256+44]
70=proba2[113*256+45]
70=proba2[113*256+46]
61=proba2[113*256+48]
39=proba2[113*256+49]
42=proba2[113*256+50]
50=proba2[113*256+51]
52=proba2[113*256+52]
45=proba2[113*256+53]
50=proba2[113*256+54]
46=proba2[113*256+55]
54=proba2[113*256+56]
44=proba2[113*256+57]
77=proba2[113*256+66]
77=proba2[113*256+68]
66=proba2[113*256+69]
77=proba2[113*256+70]
77=proba2[113*256+72]
77=proba2[113*256+74]
77=proba2[113*256+75]
77=proba2[113*256+79]
70=proba2[113*256+80]
77=proba2[113*256+81]
77=proba2[113*256+82]
66=proba2[113*256+83]
77=proba2[113*256+84]
77=proba2[113*256+85]
70=proba2[113*256+87]
70=proba2[113*256+88]
63=proba2[113*256+90]
35=proba2[113*256+97]
41=proba2[113*256+98]
37=proba2[113*256+99]
43=proba2[113*256+100]
44=proba2[113*256+101]
42=proba2[113*256+102]
50=proba2[113*256+103]
50=proba2[113*256+104]
43=proba2[113*256+105]
46=proba2[113*256+106]
52=proba2[113*256+107]
48=proba2[113*256+108]
41=proba2[113*256+109]
48=proba2[113*256+110]
53=proba2[113*256+111]
41=proba2[113*256+112]
40=proba2[113*256+113]
43=proba2[113*256+114]
35=proba2[113*256+115]
41=proba2[113*256+116]
6=proba2[113*256+117]
45=proba2[113*256+118]
30=proba2[113*256+119]
49=proba2[113*256+120]
53=proba2[113*256+121]
46=proba2[113*256+122]
34=proba1[114]
88=proba2[114*256+32]
82=proba2[114*256+33]
100=proba2[114*256+35]
89=proba2[114*256+36]
107=proba2[114*256+37]
88=proba2[114*256+38]
107=proba2[114*256+39]
100=proba2[114*256+41]
89=proba2[114*256+42]
89=proba2[114*256+43]
107=proba2[114*256+44]
84=proba2[114*256+45]
84=proba2[114*256+46]
56=proba2[114*256+48]
45=proba2[114*256+49]
50=proba2[114*256+50]
58=proba2[114*256+51]
60=proba2[114*256+52]
60=proba2[114*256+53]
59=proba2[114*256+54]
57=proba2[114*256+55]
62=proba2[114*256+56]
56=proba2[114*256+57]
107=proba2[114*256+59]
107=proba2[114*256+61]
107=proba2[114*256+63]
83=proba2[114*256+64]
107=proba2[114*256+65]
100=proba2[114*256+66]
107=proba2[114*256+67]
107=proba2[114*256+68]
100=proba2[114*256+69]
100=proba2[114*256+70]
100=proba2[114*256+71]
100=proba2[114*256+72]
100=proba2[114*256+73]
107=proba2[114*256+74]
96=proba2[114*256+75]
96=proba2[114*256+76]
93=proba2[114*256+77]
107=proba2[114*256+78]
100=proba2[114*256+79]
100=proba2[114*256+80]
96=proba2[114*256+81]
107=proba2[114*256+82]
89=proba2[114*256+83]
96=proba2[114*256+84]
96=proba2[114*256+85]
100=proba2[114*256+86]
107=proba2[114*256+87]
107=proba2[114*256+91]
96=proba2[114*256+95]
20=proba2[114*256+97]
41=proba2[114*256+98]
35=proba2[114*256+99]
30=proba2[114*256+100]
18=proba2[114*256+101]
45=proba2[114*256+102]
37=proba2[114*256+103]
54=proba2[114*256+104]
19=proba2[114*256+105]
56=proba2[114*256+106]
44=proba2[114*256+107]
38=proba2[114*256+108]
39=proba2[114*256+109]
36=proba2[114*256+110]
21=proba2[114*256+111]
44=proba2[114*256+112]
62=proba2[114*256+113]
35=proba2[114*256+114]
35=proba2[114*256+115]
28=proba2[114*256+116]
35=proba2[114*256+117]
46=proba2[114*256+118]
54=proba2[114*256+119]
66=proba2[114*256+120]
39=proba2[114*256+121]
57=proba2[114*256+122]
96=proba2[114*256+125]
26=proba1[115]
84=proba2[115*256+32]
84=proba2[115*256+33]
93=proba2[115*256+35]
93=proba2[115*256+36]
90=proba2[115*256+37]
86=proba2[115*256+38]
104=proba2[115*256+39]
104=proba2[115*256+41]
86=proba2[115*256+42]
84=proba2[115*256+43]
90=proba2[115*256+44]
83=proba2[115*256+45]
79=proba2[115*256+46]
50=proba2[115*256+48]
41=proba2[115*256+49]
45=proba2[115*256+50]
54=proba2[115*256+51]
56=proba2[115*256+52]
55=proba2[115*256+53]
55=proba2[115*256+54]
53=proba2[115*256+55]
59=proba2[115*256+56]
51=proba2[115*256+57]
104=proba2[115*256+59]
104=proba2[115*256+60]
104=proba2[115*256+61]
97=proba2[115*256+63]
88=proba2[115*256+64]
97=proba2[115*256+65]
104=proba2[115*256+66]
104=proba2[115*256+67]
83=proba2[115*256+68]
104=proba2[115*256+69]
93=proba2[115*256+70]
104=proba2[115*256+71]
97=proba2[115*256+72]
97=proba2[115*256+73]
104=proba2[115*256+75]
104=proba2[115*256+76]
86=proba2[115*256+77]
97=proba2[115*256+81]
97=proba2[115*256+82]
104=proba2[115*256+83]
97=proba2[115*256+84]
104=proba2[115*256+86]
104=proba2[115*256+87]
104=proba2[115*256+89]
81=proba2[115*256+95]
104=proba2[115*256+96]
22=proba2[115*256+97]
43=proba2[115*256+98]
30=proba2[115*256+99]
43=proba2[115*256+100]
21=proba2[115*256+101]
48=proba2[115*256+102]
47=proba2[115*256+103]
33=proba2[115*256+104]
25=proba2[115*256+105]
54=proba2[115*256+106]
38=proba2[115*256+107]
40=proba2[115*256+108]
37=proba2[115*256+109]
41=proba2[115*256+110]
26=proba2[115*256+111]
33=proba2[115*256+112]
53=proba2[115*256+113]
48=proba2[115*256+114]
23=proba2[115*256+115]
19=proba2[115*256+116]
34=proba2[115*256+117]
53=proba2[115*256+118]
46=proba2[115*256+119]
56=proba2[115*256+120]
37=proba2[115*256+121]
60=proba2[115*256+122]
31=proba1[116]
87=proba2[116*256+32]
77=proba2[116*256+33]
92=proba2[116*256+36]
83=proba2[116*256+38]
92=proba2[116*256+39]
90=proba2[116*256+42]
97=proba2[116*256+43]
103=proba2[116*256+44]
79=proba2[116*256+45]
90=proba2[116*256+46]
54=proba2[116*256+48]
44=proba2[116*256+49]
48=proba2[116*256+50]
56=proba2[116*256+51]
57=proba2[116*256+52]
60=proba2[116*256+53]
57=proba2[116*256+54]
57=proba2[116*256+55]
61=proba2[116*256+56]
54=proba2[116*256+57]
103=proba2[116*256+59]
97=proba2[116*256+61]
86=proba2[116*256+64]
87=proba2[116*256+65]
92=proba2[116*256+66]
97=proba2[116*256+68]
92=proba2[116*256+69]
103=proba2[116*256+71]
103=proba2[116*256+72]
97=proba2[116*256+73]
97=proba2[116*256+74]
103=proba2[116*256+76]
90=proba2[116*256+77]
92=proba2[116*256+78]
97=proba2[116*256+79]
103=proba2[116*256+80]
103=proba2[116*256+81]
87=proba2[116*256+82]
92=proba2[116*256+84]
103=proba2[116*256+85]
103=proba2[116*256+86]
97=proba2[116*256+88]
92=proba2[116*256+89]
81=proba2[116*256+95]
21=proba2[116*256+97]
46=proba2[116*256+98]
38=proba2[116*256+99]
48=proba2[116*256+100]
17=proba2[116*256+101]
50=proba2[116*256+102]
51=proba2[116*256+103]
24=proba2[116*256+104]
21=proba2[116*256+105]
55=proba2[116*256+106]
58=proba2[116*256+107]
43=proba2[116*256+108]
42=proba2[116*256+109]
47=proba2[116*256+110]
20=proba2[116*256+111]
47=proba2[116*256+112]
67=proba2[116*256+113]
25=proba2[116*256+114]
37=proba2[116*256+115]
29=proba2[116*256+116]
34=proba2[116*256+117]
54=proba2[116*256+118]
49=proba2[116*256+119]
60=proba2[116*256+120]
37=proba2[116*256+121]
48=proba2[116*256+122]
103=proba2[116*256+124]
51=proba1[117]
102=proba2[117*256+32]
81=proba2[117*256+33]
95=proba2[117*256+35]
91=proba2[117*256+36]
102=proba2[117*256+37]
88=proba2[117*256+38]
102=proba2[117*256+40]
86=proba2[117*256+42]
102=proba2[117*256+43]
95=proba2[117*256+44]
81=proba2[117*256+45]
84=proba2[117*256+46]
58=proba2[117*256+48]
47=proba2[117*256+49]
50=proba2[117*256+50]
58=proba2[117*256+51]
61=proba2[117*256+52]
60=proba2[117*256+53]
57=proba2[117*256+54]
58=proba2[117*256+55]
63=proba2[117*256+56]
57=proba2[117*256+57]
102=proba2[117*256+59]
102=proba2[117*256+61]
102=proba2[117*256+63]
102=proba2[117*256+64]
102=proba2[117*256+65]
91=proba2[117*256+66]
91=proba2[117*256+68]
88=proba2[117*256+70]
95=proba2[117*256+71]
102=proba2[117*256+72]
102=proba2[117*256+73]
86=proba2[117*256+75]
88=proba2[117*256+76]
102=proba2[117*256+77]
102=proba2[117*256+78]
91=proba2[117*256+79]
91=proba2[117*256+80]
86=proba2[117*256+82]
88=proba2[117*256+83]
88=proba2[117*256+84]
102=proba2[117*256+85]
102=proba2[117*256+87]
91=proba2[117*256+88]
102=proba2[117*256+90]
102=proba2[117*256+91]
102=proba2[117*256+93]
84=proba2[117*256+95]
35=proba2[117*256+97]
32=proba2[117*256+98]
29=proba2[117*256+99]
28=proba2[117*256+100]
27=proba2[117*256+101]
40=proba2[117*256+102]
36=proba2[117*256+103]
51=proba2[117*256+104]
27=proba2[117*256+105]
49=proba2[117*256+106]
42=proba2[117*256+107]
23=proba2[117*256+108]
32=proba2[117*256+109]
26=proba2[117*256+110]
51=proba2[117*256+111]
33=proba2[117*256+112]
60=proba2[117*256+113]
20=proba2[117*256+114]
23=proba2[117*256+115]
29=proba2[117*256+116]
59=proba2[117*256+117]
43=proba2[117*256+118]
61=proba2[117*256+119]
37=proba2[117*256+120]
45=proba2[117*256+121]
43=proba2[117*256+122]
38=proba1[118]
72=proba2[118*256+33]
85=proba2[118*256+35]
85=proba2[118*256+36]
85=proba2[118*256+38]
85=proba2[118*256+42]
85=proba2[118*256+43]
92=proba2[118*256+44]
92=proba2[118*256+45]
74=proba2[118*256+46]
57=proba2[118*256+48]
47=proba2[118*256+49]
50=proba2[118*256+50]
57=proba2[118*256+51]
56=proba2[118*256+52]
54=proba2[118*256+53]
57=proba2[118*256+54]
56=proba2[118*256+55]
55=proba2[118*256+56]
55=proba2[118*256+57]
85=proba2[118*256+61]
81=proba2[118*256+64]
81=proba2[118*256+65]
85=proba2[118*256+66]
92=proba2[118*256+67]
92=proba2[118*256+68]
85=proba2[118*256+69]
92=proba2[118*256+71]
78=proba2[118*256+72]
85=proba2[118*256+75]
92=proba2[118*256+76]
92=proba2[118*256+77]
92=proba2[118*256+79]
92=proba2[118*256+80]
85=proba2[118*256+81]
92=proba2[118*256+84]
81=proba2[118*256+85]
85=proba2[118*256+95]
16=proba2[118*256+97]
42=proba2[118*256+98]
44=proba2[118*256+99]
42=proba2[118*256+100]
14=proba2[118*256+101]
50=proba2[118*256+102]
50=proba2[118*256+103]
51=proba2[118*256+104]
13=proba2[118*256+105]
55=proba2[118*256+106]
56=proba2[118*256+107]
42=proba2[118*256+108]
45=proba2[118*256+109]
49=proba2[118*256+110]
27=proba2[118*256+111]
47=proba2[118*256+112]
59=proba2[118*256+113]
34=proba2[118*256+114]
45=proba2[118*256+115]
46=proba2[118*256+116]
46=proba2[118*256+117]
48=proba2[118*256+118]
58=proba2[118*256+119]
57=proba2[118*256+120]
44=proba2[118*256+121]
61=proba2[118*256+122]
43=proba1[119]
86=proba2[119*256+32]
86=proba2[119*256+33]
86=proba2[119*256+34]
72=proba2[119*256+37]
86=proba2[119*256+38]
79=proba2[119*256+42]
86=proba2[119*256+44]
79=proba2[119*256+45]
79=proba2[119*256+46]
56=proba2[119*256+48]
47=proba2[119*256+49]
45=proba2[119*256+50]
50=proba2[119*256+51]
52=proba2[119*256+52]
57=proba2[119*256+53]
55=proba2[119*256+54]
52=proba2[119*256+55]
55=proba2[119*256+56]
53=proba2[119*256+57]
79=proba2[119*256+65]
86=proba2[119*256+68]
86=proba2[119*256+69]
79=proba2[119*256+70]
86=proba2[119*256+71]
86=proba2[119*256+72]
86=proba2[119*256+73]
79=proba2[119*256+74]
86=proba2[119*256+77]
79=proba2[119*256+81]
79=proba2[119*256+83]
86=proba2[119*256+84]
75=proba2[119*256+86]
86=proba2[119*256+89]
86=proba2[119*256+90]
79=proba2[119*256+95]
16=proba2[119*256+97]
42=proba2[119*256+98]
46=proba2[119*256+99]
43=proba2[119*256+100]
14=proba2[119*256+101]
46=proba2[119*256+102]
47=proba2[119*256+103]
38=proba2[119*256+104]
20=proba2[119*256+105]
53=proba2[119*256+106]
46=proba2[119*256+107]
44=proba2[119*256+108]
47=proba2[119*256+109]
39=proba2[119*256+110]
22=proba2[119*256+111]
49=proba2[119*256+112]
55=proba2[119*256+113]
42=proba2[119*256+114]
34=proba2[119*256+115]
46=proba2[119*256+116]
45=proba2[119*256+117]
59=proba2[119*256+118]
36=proba2[119*256+119]
43=proba2[119*256+120]
40=proba2[119*256+121]
49=proba2[119*256+122]
51=proba1[120]
82=proba2[120*256+37]
76=proba2[120*256+38]
82=proba2[120*256+39]
82=proba2[120*256+42]
82=proba2[120*256+43]
76=proba2[120*256+44]
61=proba2[120*256+45]
66=proba2[120*256+46]
40=proba2[120*256+48]
31=proba2[120*256+49]
35=proba2[120*256+50]
39=proba2[120*256+51]
44=proba2[120*256+52]
47=proba2[120*256+53]
42=proba2[120*256+54]
42=proba2[120*256+55]
45=proba2[120*256+56]
41=proba2[120*256+57]
82=proba2[120*256+59]
82=proba2[120*256+63]
82=proba2[120*256+64]
82=proba2[120*256+65]
82=proba2[120*256+68]
82=proba2[120*256+70]
76=proba2[120*256+71]
76=proba2[120*256+72]
82=proba2[120*256+76]
76=proba2[120*256+77]
76=proba2[120*256+80]
76=proba2[120*256+81]
82=proba2[120*256+82]
82=proba2[120*256+83]
82=proba2[120*256+84]
76=proba2[120*256+85]
82=proba2[120*256+86]
82=proba2[120*256+87]
82=proba2[120*256+88]
82=proba2[120*256+90]
82=proba2[120*256+93]
82=proba2[120*256+95]
24=proba2[120*256+97]
38=proba2[120*256+98]
32=proba2[120*256+99]
40=proba2[120*256+100]
25=proba2[120*256+101]
37=proba2[120*256+102]
44=proba2[120*256+103]
48=proba2[120*256+104]
24=proba2[120*256+105]
48=proba2[120*256+106]
49=proba2[120*256+107]
38=proba2[120*256+108]
34=proba2[120*256+109]
45=proba2[120*256+110]
31=proba2[120*256+111]
32=proba2[120*256+112]
51=proba2[120*256+113]
41=proba2[120*256+114]
36=proba2[120*256+115]
31=proba2[120*256+116]
40=proba2[120*256+117]
45=proba2[120*256+118]
42=proba2[120*256+119]
24=proba2[120*256+120]
33=proba2[120*256+121]
45=proba2[120*256+122]
45=proba1[121]
75=proba2[121*256+32]
72=proba2[121*256+33]
91=proba2[121*256+34]
80=proba2[121*256+36]
78=proba2[121*256+38]
91=proba2[121*256+39]
91=proba2[121*256+41]
69=proba2[121*256+42]
78=proba2[121*256+44]
69=proba2[121*256+45]
84=proba2[121*256+46]
43=proba2[121*256+48]
34=proba2[121*256+49]
38=proba2[121*256+50]
48=proba2[121*256+51]
46=proba2[121*256+52]
51=proba2[121*256+53]
47=proba2[121*256+54]
44=proba2[121*256+55]
49=proba2[121*256+56]
43=proba2[121*256+57]
91=proba2[121*256+59]
78=proba2[121*256+63]
91=proba2[121*256+64]
84=proba2[121*256+65]
84=proba2[121*256+66]
80=proba2[121*256+67]
73=proba2[121*256+68]
91=proba2[121*256+69]
91=proba2[121*256+72]
84=proba2[121*256+74]
84=proba2[121*256+76]
78=proba2[121*256+77]
91=proba2[121*256+78]
84=proba2[121*256+80]
91=proba2[121*256+82]
80=proba2[121*256+83]
66=proba2[121*256+84]
78=proba2[121*256+85]
80=proba2[121*256+88]
91=proba2[121*256+89]
91=proba2[121*256+93]
78=proba2[121*256+95]
22=proba2[121*256+97]
28=proba2[121*256+98]
33=proba2[121*256+99]
35=proba2[121*256+100]
27=proba2[121*256+101]
44=proba2[121*256+102]
32=proba2[121*256+103]
49=proba2[121*256+104]
48=proba2[121*256+105]
47=proba2[121*256+106]
44=proba2[121*256+107]
27=proba2[121*256+108]
32=proba2[121*256+109]
31=proba2[121*256+110]
24=proba2[121*256+111]
35=proba2[121*256+112]
63=proba2[121*256+113]
30=proba2[121*256+114]
25=proba2[121*256+115]
37=proba2[121*256+116]
39=proba2[121*256+117]
37=proba2[121*256+118]
48=proba2[121*256+119]
48=proba2[121*256+120]
47=proba2[121*256+121]
45=proba2[121*256+122]
44=proba1[122]
80=proba2[122*256+33]
87=proba2[122*256+34]
87=proba2[122*256+35]
87=proba2[122*256+36]
87=proba2[122*256+37]
80=proba2[122*256+38]
76=proba2[122*256+40]
87=proba2[122*256+42]
76=proba2[122*256+44]
73=proba2[122*256+45]
53=proba2[122*256+48]
46=proba2[122*256+49]
46=proba2[122*256+50]
46=proba2[122*256+51]
49=proba2[122*256+52]
53=proba2[122*256+53]
49=proba2[122*256+54]
52=proba2[122*256+55]
49=proba2[122*256+56]
52=proba2[122*256+57]
87=proba2[122*256+59]
80=proba2[122*256+64]
80=proba2[122*256+65]
87=proba2[122*256+66]
87=proba2[122*256+67]
87=proba2[122*256+68]
87=proba2[122*256+70]
80=proba2[122*256+71]
80=proba2[122*256+73]
80=proba2[122*256+75]
87=proba2[122*256+76]
73=proba2[122*256+77]
87=proba2[122*256+78]
87=proba2[122*256+81]
87=proba2[122*256+83]
87=proba2[122*256+85]
87=proba2[122*256+87]
80=proba2[122*256+88]
73=proba2[122*256+89]
80=proba2[122*256+95]
18=proba2[122*256+97]
44=proba2[122*256+98]
48=proba2[122*256+99]
46=proba2[122*256+100]
17=proba2[122*256+101]
51=proba2[122*256+102]
47=proba2[122*256+103]
41=proba2[122*256+104]
21=proba2[122*256+105]
60=proba2[122*256+106]
50=proba2[122*256+107]
44=proba2[122*256+108]
38=proba2[122*256+109]
48=proba2[122*256+110]
18=proba2[122*256+111]
50=proba2[122*256+112]
53=proba2[122*256+113]
46=proba2[122*256+114]
49=proba2[122*256+115]
45=proba2[122*256+116]
34=proba2[122*256+117]
54=proba2[122*256+118]
47=proba2[122*256+119]
45=proba2[122*256+120]
35=proba2[122*256+121]
24=proba2[122*256+122]
87=proba2[122*256+126]
104=proba1[123]
19=proba2[123*256+75]
19=proba2[123*256+91]
19=proba2[123*256+97]
19=proba2[123*256+102]
19=proba2[123*256+114]
19=proba2[123*256+122]
19=proba2[123*256+124]
111=proba1[124]
20=proba2[124*256+38]
20=proba2[124*256+41]
20=proba2[124*256+45]
20=proba2[124*256+53]
20=proba2[124*256+82]
20=proba2[124*256+106]
20=proba2[124*256+108]
20=proba2[124*256+109]
118=proba1[125]
13=proba2[125*256+54]
13=proba2[125*256+66]
13=proba2[125*256+101]
13=proba2[125*256+115]
111=proba1[126]
13=proba2[126*256+86]
13=proba2[126*256+98]
13=proba2[126*256+115]
13=proba2[126*256+122]
