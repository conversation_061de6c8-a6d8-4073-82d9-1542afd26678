How to use the 'dynamic' format within john.

<PERSON> has been enhanced to be able to handle MANY MD5/SHA1 and other cipher
variants easily.

'variants' are things like:

md5($p)      (Note, $p is the password, $s will be the salt, etc)
md5(md5($p)) (Note, this is md5 of the 32 byte result of a prior md5)
md5($p.$s)   (Note, . is the concatenate operator, so $p.$s is
              password with salt appended)
sha1($p)     (Note, $p is password, and sha1 is the cipher).
sha1(md5($p)) Here we first run md5 on password, then sha1 on the
              resultant 'hex' string.
sha512($s.sha512($p.$s).$p)  This uses a couple of calls to sha512.
              The intermedidate results are possible to be in
              base-16 (or base-16 uppercase), base-64 (MIME), or
              raw 64 bytes.
...

Most of the above rules are php 'style' rules, but I think in php,
$pass is used, and $salt is used.  In this document, they have been
simplified to $p and $s.

This is done by using a new file format string:  dynamic_#   where
the # is a number.  <PERSON> reserves numbers from dynamic_0 up to
dynamic_999 for 'built-in' formats.  Not all are defined at this
time, but those have been reserved.

The format of the input file lines will be:

userID:$dynamic_#$base_16_hash[$salt]

Salts can contain some problematic characters. Some of these would be
characters such as:  :  \r \n \ NULL, etc.  The : (colon) char is used
by JtR as a field separator. If it exists in the salt, it will split
the salt into multiple fields (i.e. wrong).  A carriage return or line
feed, will cause the line to split, and JtR to not read it right.
NULL bytes are a problem in any C file, using normal 'string' functions.
The \ char, is used as a quoting char within dynamic, and can cause issues.
One other recently found issue, is if the salt ends in ' ' or '\t' (space
or tab), then if the dyna hash is placed on a line by itself (i.e. without
a user: prepended), then jtr will trim the whitespace off. This is a known
issue, and this is just a bad side effect of a known wanted behavior where
the raw hash files are cleaned up during processing.
DUE to all of these problems, dynamic has been expanded to take this as
the salt:    $dyanamic_#$base_16_hash$HEX$hex_bytes_of_salt.  In this
format, if the salt was 1234, then $HEX$31323334 would be the equivalent
value.  This allows salts such as $\x0:\r\nBadSalt to actually be encoded.
This salt CAN be used, if you use the representation:
$HEX$003a0a0d42616453616c74.
There is an application in ./run which can 'help' This is ./run/raw2dyna
This process will convert files in the format of hashCsalt (c can be any char),
into the proper $dynamic_#$hash$salt   It can also handle saltChash (if
salt is a fixed size).  It also properly handle the problematic characters,
and uses the $HEX$hex_salt method, if there are bad characters in the salt.

There may be a 2nd salt added in the near future, but not sure how it
will be implemented.

Here is an 'example' of the format required:

userID:$dynamic_0$28fc2782ea7ef51c1104ccf7b9bea13d

which is 'raw' md5, with a password of 1402

userID:$dynamic_6$8d8cac84f234f42e32daeb94e7cd49e8$*.*

which is in the vBulletin format:  md5(md5($p).$s), and which has a
fixed 3 byte salt (the $*.* above is the salt signature, and the
salt is *.*), solomon1 is the password for the above hash.  NOTE
vBulletin is actually dynamic_7. It is the expression format, but
puts to additional constraints on input. dynamic_7 forces a 3 byte
salt, and it will NOT allow ':' character to be used as a separator,
since vBulletin salts can contain that character.  However, the
above dynamic_6 works for this document, since *.* salt does not
have a ':'

Expressions can be added to john.conf (john.ini), dynamic.conf
(included by john.conf) or john.local.conf to allow an end user
to add a new format type, without having to do ANY coding at all.
The end user would have to learn the $dynamic_n$
primitives, and how the data is used (input data, key data,
encryption data, etc).  Also, the user would have to build
properly formatted SAMPLE test values (john requires this).
For the full 'HOW TO USE' documentation, see the dynamic.conf.


How to build the 'test' value is beyond the scope of this readme,
but a trivial example might help.
Expression format:  md5(md5($p).md5($p))
Password test1
md5(test1) == 5a105e8b9d40e1329780d62ea2265d8a
so we want to md5 that result concatenated
so md5(md5(test1).md5(test1)) is the same as
md5(5a105e8b9d40e1329780d62ea2265d8a5a105e8b9d40e1329780d62ea2265d8a)
which equals 478b10974f15e7295883224fd286ccba
so a proper 'test' line is:
Test=$dynamic_1003$478b10974f15e7295883224fd286ccba:test1
( as can be seen in dynamic.conf for dynamic_1003 )

To get a list of all dynamic formats, you can use

./john --list=subformats

This command will create output like this:

Format = dynamic_0   type = dynamic_0: md5($p) (raw-md5)
Format = dynamic_1   type = dynamic_1: md5($p.$s) (joomla)
Format = dynamic_2   type = dynamic_2: md5(md5($p)) (e107)
...
Format = dynamic_32  type = dynamic_32: md4($p.$s)
Format = dynamic_33  type = dynamic_33: md4(unicode($p))
Format = dynamic_34  type = dynamic_34: md5(md4($p))
UserFormat = dynamic_1001  type = dynamic_1001 md5(md5(md5(md5($p))))
UserFormat = dynamic_1002  type = dynamic_1002 md5(md5(md5(md5(md5($p)))))
UserFormat = dynamic_1003  type = dynamic_1003 md5(md5($p).md5($p))

The "Format = ..." lines list builtin formats, the lines "UserFormat = ..."
list user formats (or sample formats).

The builtiin formats that john has right now are:

------------+-------------------------+-------+------------------
Signature   | expression              | Salt  | notes
------------+-------------------------+-------+------------------
dynamic_0   | md5($p)                 | No    | raw-md5
dynamic_1   | md5($p.$s)              | Yes   | (joomla)
dynamic_2   | md5(md5($p))            | No    | double md5 (e107)
dynamic_3   | md5(md5(md5($p)))       | No    | triple md5
dynamic_4   | md5($s.$p)              | Yes   | (OS Commerce)
dynamic_5   | md5($s.$p.$s)           | Yes   |
dynamic_6   | md5(md5($p).$s)         | Yes   |
dynamic_7   | md5(md5($p).$s)         | Yes   | vBulletin. 3 byte salt, salt uses ':' char
dynamic_8   | md5(md5($s).$p)         | Yes   |
dynamic_9   | md5($s.md5($p))         | Yes   |
dynamic_10  | md5($s.md5($s.$p))      | Yes   |
dynamic_11  | md5($s.md5($p.$s))      | Yes   |
dynamic_12  | md5(md5($s).md5($p))    | Yes   | (IPB)
dynamic_13  | md5(md5($p).md5($s))    | Yes   |
dynamic_14  | md5($s.md5($p).$s)      | Yes   |
dynamic_15  | md5($u.md5($p).$s)      | Yes   |
dynamic_16  | md5(md5(md5($p).$s).$s2)| Yes   |
dynamic_17  | phpass ($H$ and $P$)    | Yes   | phpass and PHPbb-v3 hashes
dynamic_18  | PostOffice MD5 (PO)     | Yes   | md5($s.Y.$p.\xF7.$s) (Post.Office MD5)
dynamic_19  | Cisco PIX               | No    |
dynamic_20  | Cisco PIX (salted)      | Yes   |
dynamic_21  | HTTP Digest Access Auth | Yes   | Contains a 'special' salt function.
dynamic_22  | md5(sha1($p))           | No    | Use sha1 internally, but sill md5 hash (32 byte hex number)
dynamic_23  | sha1(md5($p))           | No    | Requires a 40 byte hex number (valid sha1 input)
dynamic_24  | sha1($p.$s)             | Yes   | Requires a 40 byte hex number (valid sha1 input)
dynamic_25  | sha1($s.$p)             | Yes   | Requires a 40 byte hex number (valid sha1 input)
dynamic_26  | sha1($p) (raw-sha1)     | No    | Requires a 40 byte hex number (valid sha1 input)
dynamic_27  | FreeBSD md5             | Yes   |
dynamic_28  | Apache MD5              | Yes   |
dynamic_29  | md5(unicode($p))        | No    |
dynamic_30  | md4($p) (raw-md4)       | No    |
dynamic_31  | md4($s.$p)              | Yes   |
dynamic_32  | md4($p.$s)              | Yes   |
dynamic_33  | md4(unicode($p))        | No    |
dynamic_34  | md5(md4($p))            | No    |
dynamic_35  | sha1(uc($u).:.$p)       | Yes   | ManGOS
dynamic_36  | sha1($u.:.$p)           | Yes   | ManGOS2
dynamic_37  | sha1(lc($u).$p)         | Yes   | SMF
dynamic_38  | sha1($s.sha1($s.sha1($p)))| Yes | Wolt3BB
dynamic_39  | md5($s.pad_16($p))      | Yes   | Net-md5.  Long salt, passwords are 16 bytes, null padded for under 16 bytes
dynamic_40  | sha1($s.pad_16($p))     | Yes   | Net-sha1.  Long salt, passwords are 16 bytes, null padded for under 16 bytes
dynamic_50  | sha224($p)              | No    |
dynamic_51  | sha224($s.$p)           | Yes   |
dynamic_52  | sha224($p.$s)           | Yes   |
dynamic_53  | sha224(sha224($p))      | No    |
dynamic_54  | sha224(sha224_raw($p))  | No    |
dynamic_55  | sha224(sha224($p).$s)   | Yes   |
dynamic_56  | sha224($s.sha224($p))   | Yes   |
dynamic_57  | sha224(sha224($s).sha224($p)) | Yes   |
dynamic_58  | sha224(sha224($p).sha224($p)) | No    |
dynamic_60  | sha256($p)              | No    |
dynamic_61  | sha256($s.$p)           | Yes   |
dynamic_62  | sha256($p.$s)           | Yes   |
dynamic_63  | sha256(sha256($p))      | No    |
dynamic_64  | sha256(sha256_raw($p))  | No    |
dynamic_65  | sha256(sha256($p).$s)   | Yes   |
dynamic_66  | sha256($s.sha256($p))   | Yes   |
dynamic_67  | sha256(sha256($s).sha256($p)) | Yes   |
dynamic_68  | sha256(sha256($p).sha256($p)) | No    |
dynamic_70  | sha384($p)              | No    |
dynamic_71  | sha384($s.$p)           | Yes   |
dynamic_72  | sha384($p.$s)           | Yes   |
dynamic_73  | sha384(sha384($p))      | No    |
dynamic_74  | sha384(sha384_raw($p))  | No    |
dynamic_75  | sha384(sha384($p).$s)   | Yes   |
dynamic_76  | sha384($s.sha384($p))   | Yes   |
dynamic_77  | sha384(sha384($s).sha384($p)) | Yes   |
dynamic_78  | sha384(sha384($p).sha384($p)) | No    |
dynamic_80  | sha512($p)              | No    |
dynamic_81  | sha512($s.$p)           | Yes   |
dynamic_82  | sha512($p.$s)           | Yes   |
dynamic_83  | sha512(sha512($p))      | No    |
dynamic_84  | sha512(sha512_raw($p))  | No    |
dynamic_85  | sha512(sha512($p).$s)   | Yes   |
dynamic_86  | sha512($s.sha512($p))   | Yes   |
dynamic_87  | sha512(sha512($s).sha512($p)) | Yes   |
dynamic_88  | sha512(sha512($p).sha512($p)) | No    |
dynamic_90  | GOST($p)                | No    |
dynamic_91  | GOST($s.$p)             | Yes   |
dynamic_92  | GOST($p.$s)             | Yes   |
dynamic_93  | GOST(GOST($p))          | No    |
dynamic_94  | GOST(GOST_raw($p))      | No    |
dynamic_95  | GOST(GOST($p).$s)       | Yes   |
dynamic_96  | GOST($s.GOST($p))       | Yes   |
dynamic_97  | GOST(GOST($s).GOST($p)) | Yes   |
dynamic_98  | GOST(GOST($p).GOST($p)) | No    |
dynamic_100 | WHIRLPOOL($p)           | No    |
dynamic_101 | WHIRLPOOL($s.$p)        | Yes   |
dynamic_102 | WHIRLPOOL($p.$s)        | Yes   |
dynamic_103 | WHIRLPOOL(WHIRLPOOL($p))     | No    |
dynamic_104 | WHIRLPOOL(WHIRLPOOL_raw($p)) | No    |
dynamic_105 | WHIRLPOOL(WHIRLPOOL($p).$s)  | Yes   |
dynamic_106 | WHIRLPOOL($s.WHIRLPOOL($p))  | Yes   |
dynamic_107 | WHIRLPOOL(WHIRLPOOL($s).WHIRLPOOL($p)) | Yes   |
dynamic_108 | WHIRLPOOL(WHIRLPOOL($p).WHIRLPOOL($p)) | No    |
dynamic_110 | Tiger($p)                | No   |
dynamic_111 | Tiger($s.$p)             | Yes  |
dynamic_112 | Tiger($p.$s)             | Yes  |
dynamic_113 | Tiger(Tiger($p))         | No   |
dynamic_114 | Tiger(Tiger_raw($p))     | No   |
dynamic_115 | Tiger(Tiger($p).$s)      | Yes  |
dynamic_116 | Tiger($s.Tiger($p))      | Yes  |
dynamic_117 | Tiger(Tiger($s).Tiger($p)) | Yes  |
dynamic_118 | Tiger(Tiger($p).Tiger($p)) | No   |
dynamic_120 | RIPEMD128($p)                | No   |
dynamic_121 | RIPEMD128($s.$p)             | Yes  |
dynamic_122 | RIPEMD128($p.$s)             | Yes  |
dynamic_123 | RIPEMD128(RIPEMD128($p))         | No   |
dynamic_124 | RIPEMD128(RIPEMD128_raw($p))     | No   |
dynamic_125 | RIPEMD128(RIPEMD128($p).$s)      | Yes  |
dynamic_126 | RIPEMD128($s.RIPEMD128($p))      | Yes  |
dynamic_127 | RIPEMD128(RIPEMD128($s).RIPEMD128($p)) | Yes  |
dynamic_128 | RIPEMD128(RIPEMD128($p).RIPEMD128($p)) | No   |
dynamic_130 | RIPEMD160($p)                | No   |
dynamic_131 | RIPEMD160($s.$p)             | Yes  |
dynamic_132 | RIPEMD160($p.$s)             | Yes  |
dynamic_133 | RIPEMD160(RIPEMD160($p))         | No   |
dynamic_134 | RIPEMD160(RIPEMD160_raw($p))     | No   |
dynamic_135 | RIPEMD160(RIPEMD160($p).$s)      | Yes  |
dynamic_136 | RIPEMD160($s.RIPEMD160($p))      | Yes  |
dynamic_137 | RIPEMD160(RIPEMD160($s).RIPEMD160($p)) | Yes  |
dynamic_138 | RIPEMD160(RIPEMD160($p).RIPEMD160($p)) | No   |
dynamic_140 | RIPEMD256($p)                | No   |
dynamic_141 | RIPEMD256($s.$p)             | Yes  |
dynamic_142 | RIPEMD256($p.$s)             | Yes  |
dynamic_143 | RIPEMD256(RIPEMD256($p))         | No   |
dynamic_144 | RIPEMD256(RIPEMD256_raw($p))     | No   |
dynamic_145 | RIPEMD256(RIPEMD256($p).$s)      | Yes  |
dynamic_146 | RIPEMD256($s.RIPEMD256($p))      | Yes  |
dynamic_147 | RIPEMD256(RIPEMD256($s).RIPEMD256($p)) | Yes  |
dynamic_148 | RIPEMD256(RIPEMD256($p).RIPEMD256($p)) | No   |
dynamic_150 | RIPEMD320($p)                | No   |
dynamic_151 | RIPEMD320($s.$p)             | Yes  |
dynamic_152 | RIPEMD320($p.$s)             | Yes  |
dynamic_153 | RIPEMD320(RIPEMD320($p))         | No   |
dynamic_154 | RIPEMD320(RIPEMD320_raw($p))     | No   |
dynamic_155 | RIPEMD320(RIPEMD320($p).$s)      | Yes  |
dynamic_156 | RIPEMD320($s.RIPEMD320($p))      | Yes  |
dynamic_157 | RIPEMD320(RIPEMD320($s).RIPEMD320($p)) | Yes  |
dynamic_158 | RIPEMD320(RIPEMD320($p).RIPEMD320($p)) | No   |


........    | RESERVED                | UNK   |
dynamic_999 | RESERVED                | UNK   |

The 'samples' stored in dynamic.conf (to be used as is, or as examples) are:

dynamic_1001  md5(md5(md5(md5($p))))
dynamic_1002  md5(md5(md5(md5(md5($p)))))
dynamic_1003  md5(md5($p).md5($p))
dynamic_1004  md5(md5(md5(md5(md5(md5($p))))))
dynamic_1005  md5(md5(md5(md5(md5(md5(md5($p)))))))
dynamic_1006  md5(md5(md5(md5(md5(md5(md5(md5($p))))))))
dynamic_1007  md5(md5($p).$s) (vBulletin)
dynamic_1008  md5($p.$s) (joomla)
# and many other newer items, added from user requests.
dynamic_1010  md5($p null_padded_to_len_100) RAdmin v2.x MD5
dynamic_1011  md5($p.md5($s)) (webEdition CMS)
dynamic_1012  md5($p.md5($s)) (webEdition CMS)  (much faster)
dynamic_1013  md5($p.PMD5(username)) (webEdition CMS)  (fast speed, but user name is stored in md5 hash format)
dynamic_1014  md5($p.$s) (long salt)
dynamic_1015  md5(md5($p.$u).$s)
dynamic_1018  md5(sha1(sha1($pass)))
dynamic_1019  md5(sha1(sha1(md5($pass))))
dynamic_1020  md5(sha1(md5($pass)))
dynamic_1021  md5(sha1(md5(sha1($pass))))
dynamic_1022  md5(sha1(md5(sha1(md5($pass)))))
dynamic_1023  sha1($pass) (hash truncated to length 32)
dynamic_1024  sha1(md5($pass)) (hash truncated to length 32)
dynamic_1025  sha1(md5(md5($pass))) (hash truncated to length 32)
dynamic_1026  sha1(sha1($pass))) (hash truncated to length 32)
dynamic_1027  sha1(sha1(sha1($pass)))) (hash truncated to length 32)
dynamic_1028  sha1(sha1_raw($pass)) (hash truncated to length 32)
dynamic_1029  sha256($pass) (hash truncated to length 32)
dynamic_1030  Whirlpool($pass) (hash truncated to length 32)
dynamic_1031  GOST($pass) (hash truncated to length 32)
dynamic_1300  md5(md5_raw($pass))
dynamic_1350  md5(md5($s.$p):$s)


NOTE the documentation section of dynamic.conf is listed here.  It shows
how to setup dynamiceric functions (above 1000).  All of the primitives
are defined, the data structures are defined, the expression, flags,
saltlen, etc.  Following the 'howto use' section, are some working
examples (this whole list is also found in dynamic.conf).

####################################################################
####################################################################
# here are some examples of DYNAMIC formats
####################################################################
####################################################################

####################################################################
# Documenation of how to 'setup' DYNAMIC formats
####################################################################
#
#   Variables / workspaces:
#
# keys[]     -  Array Used to stored keys (optionally used, we 'may' be able to use input field 1 array)
# input1[]   -  Array used for encryption, key appending, salt appending, etc happens to/from these
#                   NOTE if DynamicFunc__InitialLoadKeysToInput then this is used to store the keys
#                   and MUST NOT be damaged, since any later key retrieval will come from this buffer.
#                   This is done as an optimization. Thus input2 is required for additional scratch buffer work.
# output1[]  -  Array encryption happens and output is put here.
#                   NOTE final result MUST be put here. This is what the 'hash' array is checked against.
# salt       -  the salt is put here
# salt2      -  2nd salt ($$2)
# input2[]   -  Array same as input 1, but a totally different array
# output2[]  -  Array same as output 2, but totally different array  (can not be used as 'final' result)
# userid     -  User name (fld1). Can be done normal, upper or lower case
# fld0 to fld9 - data from fld1 to fld9 of the input file.
#
#   NOTE within the scratch work spaces (input1[] and input2[] arrays), we can only do things like append_keys
#   append_salt or other things.  Thus, each work space keeps track of each array element (knowing its contents),
#   and keeps track of the length.  In the way the scripting works, we can clear them (sets lengths of all
#   elements to 0, and 'might' clear out the actual memory).  We can append to them (keys, output conversions,
#   salt, etc) or we can force set their length to 32 ('special' optimization used in conjunction with
#   DynamicFunc__InitialLoadKeys_md5crypt_ToOutput2_Base16_to_Input1).
#
#   NOTE there is NO length validation done, other than input keys are reduced to the max allowable length.
#   Thus, if building, and you attempt something like: clear_keys append_keys append_keys append_keys append_keys
#   append_keys append_keys and the keys are too long, then they will overflow.  The maximal sizes of the
#   input arrays are 95 bytes for x86 and 54 bytes for SSE/MMX.   Above this will simply give invalid results
#   (garbage in, garbage out).
#
#####################################################################################
#
#   Flags= values. These flags are 'special' loader flags. They are optional. If used,
#   then only ONE of them can be used.
#
#################
#
#    Special input flags (all flags listed later).  They have certain 'side-effects', and some have special
#    rules that MUST be maintained within the script, and thus may not be usable for all scripts.
#    These are here mostly for optimization.  They do NOT have to be used.  If not used, then when
#    john adds keys one at a time, they are placed into the array of keys.  Then, when loading keys
#    the keys are copied from the keys array, into the input1 (2) as needed.
#
#  MGF_KEYS_INPUT
#     It will not use the keys[] array, but put the keys right
#     into the input1[] array.  This buffer must not be destroyed. john will NOT use the keys[]
#     array, and later retrieval of the key will happen from here (if there are any hashes broken).
#     For this to be able to be used.
#        $pass must be at the START of the expression.  expressions like md5($s.$p) would not work, since
#             $pass is not the 'start' of the expression.
#        the $pass expression 'may' have to be all by itself.  Thus md5($p.$s) would not work (such as joomla)
#             but md5(md5($p).$s) can be made to work.
#        The input1[] array must NOT be touched (after it is loaded).  Thus the below set of functions can NOT
#        be used in the script.
#               DynamicFunc__append_keys
#               DynamicFunc__append_keys2
#               DynamicFunc__clean_input
#               DynamicFunc__append_salt
#               DynamicFunc__append_from_last_output2_to_input1_as_base16
#               DynamicFunc__overwrite_from_last_output2_to_input1_as_base16_no_size_fix
#               DynamicFunc__append_from_last_output_as_base16
#               DynamicFunc__overwrite_from_last_output_as_base16_no_size_fix
#               DynamicFunc__append_2nd_salt DynamicFunc__append_userid
#               DynamicFunc__append_2nd_salt
#               DynamicFunc__set_input_len_32
#               DynamicFunc__set_input_len_64
#               DynamicFunc__overwrite_salt_to_input1_no_size_fix
#               DynamicFunc__append_input_from_input2
#               and the flag MGF_KEYS_UNICODE_B4_CRYPT
#
#  MGF_KEYS_CRYPT_IN2
#     This will load keys into the keys[] array.  It will then perform a md5() and put the ouput
#     into output2.   This was done a signficant enhancement for forms like md5(md5($p).$s) so that we
#     load ALL of the inner md5($p) only once, then re-used them over and over again for each new salt.
#
#  MGF_KEYS_BASE16_IN1
#     This is like the above flag, but it takes the extra step of storing the base16 conversion
#     of the md5 into input1[] array.  Due to this function, we have md5(md5($p).$s) actually working
#     FASTER than simple md5($p)  (if there are many salts).  This is a significant optimization if it can
#     be used properly.
#  MGF_KEYS_BASE16_X86_IN1
#     same as MGF_KEYS_BASE16_IN1, however, if running in SSE mode, the loader will switch to X86 mode,
#     prior to outputting the base-16 back to input1.  NOTE, the initial crypt IS done in SSE mode.
#
#  MGF_KEYS_BASE16_IN1_Offset32
#     This flag is a not valid for SSE2 (will build a 64 byte password, and 54 bytes is max SSE2 size)
#     Psudo function that does the same work as DynamicFunc__InitialLoadKeys_md5crypt_ToOutput2_Base16_to_Input1
#     however, it loaded the hext to offset 32 (instead of the 'start' of the buffer). When done, the
#     input1[] buffers will have 64 byte passwords.  The first 32 bytes are null, and the last
#     32 bytes are the hex values.  This function is for md5(md5($s).md5($p)) (but certainly could
#     be used for other formats.
#  MGF_KEYS_BASE16_X86_IN1_Offset32
#     Same as MGF_KEYS_BASE16_IN1_Offset32, but the output is in x86.  NOTE, if running in SSE mode, then
#     both MGF_KEYS_BASE16_IN1_Offset32 and MGF_KEYS_BASE16_X86_IN1_Offset32 perform exactly the same. Neither
#     one of them 'work' in SSE mode, since we will end up with a 64 byte input string (not SSE valid).
#
#  MGF_KEYS_UNICODE_B4_CRYPT
#     Switch into 'unicode' converstion mode, prior to doing the 'early' MGF_KEYS* flags. The flags which
#     can be used with this are: MGF_KEYS_CRYPT_IN2, MGF_KEYS_BASE16_IN1, MGF_KEYS_BASE16_X86_IN1,
#     MGF_KEYS_BASE16_IN1_Offset32 (and the x86 versions).    Note, that MGF_KEYS_INPUT can NOT be used
#     with this flag.
#
#  MGF_PHPassSetup
#     Special flag used to link the salt, set_salt and set_hash members of the format object to the 'special'
#     functions for phpass encryption.
#
#  MGF_POSetup
#     Special flag used for the special "Post Office" Crypt function.
#
#  Depricated 'pseudo' functions (must be first function).  These functions still work, but have been depricated.
#  dynamic simply 'transforms' them into the current flag, at script loading time.  It is best to NOT use
#  these functions in any new scripts, and to remove any usage of them.  Since these are depricated, they have
#  KEPT the already depricated MD5GenBaseFunc__ 'signatures', and have not been 'updated' to the new 'dynamic'
#  format signature.  These SHOULD NOT be used.
#	  MD5GenBaseFunc__PHPassSetup   == MGF_PHPassSetup
#	  MD5GenBaseFunc__InitialLoadKeysToInput  ==  MGF_KEYS_INPUT
#	  MD5GenBaseFunc__InitialLoadKeys_md5crypt_ToOutput2  ==  MGF_KEYS_CRYPT_IN2
#	  MD5GenBaseFunc__InitialLoadKeys_md5crypt_ToOutput2_Base16_to_Input1  == MGF_KEYS_BASE16_IN1
#	  MD5GenBaseFunc__InitialLoadKeys_md5crypt_ToOutput2_Base16_to_Input1_offset32  ==  MGF_KEYS_BASE16_IN1_Offset32
#
####################################################################
#
#   Flag= line(s)   Optional, but required IF the format needs them.
#                   Multiple flags ARE allowed.
#
#################
#
#  Flags are just that, switches that tell the dynamic parser that
#  certain things need to be handled.  One common used flag is MGF_SALTED
#  If the format is a salted hash, then this flag must be present.
#  Here is the list of flags, and what they mean, and when you should
#  use them
#
#  Flag=MGF_SALTED
#     Use this for any salted format
#
#  Flag=MGF_SALTED2
#     If there are 2 salts in the format, use this flag.  NOTE 2 salts
#     is defined, but has not be implemented yet in john.
#
#  Flag=MGF_ColonNOTValid
#     If there is some reason why the : char should not be used by john
#     (such as the : is a valid character in the salt.  If this is
#     set, then the character to be used should be put into the Test=
#     lines, AND the user should use that on the command line
#
#  Flag=MGF_NOTSSE2Safe
#     The max encryption the SSE2 code can handle is 54 bytes.  The max
#     size encryption for the 'generic' is 125 bytes.  So, if it is
#     OBVIOUS that sse2 can not be used, due to encryption lengths,
#     then set this flag.  An example is md5(md5($p).md5($s))  In this
#     case, will be 3 md5's.  2 of them will be over the salt, and
#     one over the password.  Thus, if the password and salt are each
#     under 54 bytes, then sse2 could be used.  HOWEVER, the 3rd md5
#     (the final one), is done against the concatenation of 2 32 byte
#     strings (the md5 results of the first 2 encryptions).  THUS we
#     KNOW this is a 64 byte string. That will not work in the current
#     SSE2 (or mmx) code, and thus, the SSE2/MMX builds can NOT be used
#     to test that format.
#
#  Flag=MGF_FLAT_BUFFERS
#     This is similar to MGF_NOTSSE2Safe in 'some' ways. It will cause
#     dynamic to only use the flat buffers. However, it is used for
#     large formats which HAVE SSE2 code.  These formats will use the
#     the data from the flat buffers directly (even nulling them out
#     and placing the # of bits into the right locations, and the 0x80
#     trailing byte also).  So this tells dyna to store the data and
#     intermediate flat, but use the special SSE2 code for this format
#     that loads from flat.  Currently, all formats which has SIMD code
#     have flat buffer behavior.  NOTE most (all except md5/md4 and some
#     for sha1), ONLY have the flat buffer code.  These other 3 formats
#     do have custom 'mixed' coding.
#     One really nice thing about doing SIMD this way is the single limb
#     limit has been removed.  The SIMD code is currently in MD4/5, SHA1,
#     and 32 bit SHA2 (SHA224/SHA256). The SIMD code has not been done
#     for the flat SHA384/SHA512 code yet.
#
#  Flag=MGF_INPBASE64
#     If the hashes are in base-64 (such as phpass), then use this
#     flag.  Note, the base 64 'format' used is that of phpass.  If there
#     are other orderings, then this will not work.  I know of no formats
#     in use, other than phpass (phpbb, wordpress, etc) which use base-64
#     for md5.  This is actually the same format as CryptBS in base64conv
#     utility.  This has been improved now. It should work for any standard
#     length hash (from 16 bytes up to 64 bytes).  The only part of the
#     hash that is base-64 decoded is within the binary hash value itself.
#     dynamic does not handle base-64 decoding within any part of the salt.
#
#  Flag=MGF_INPBASE64b
#     This is like the MGF_INPBASE64 flag, BUT the encoding is in BE format
#     crypt.  This is the same as Crypt encoding in base64conv utility
#
#  Flag=MGF_INPBASE64m
#     This is like the MGF_INPBASE64 flag, BUT the encoding is in MIME.
#     This is the same as MIME encoding in base64conv utility. NOTE, the
#     hash can end with optional '=' place holding characters. These will
#     be stripped off, in the prepare/split functions, to canonicalize the
#     value of the hash to be NON-equal padded.  But an input file can have
#     the '=' padding or not (or even mixed) and still process properly.
#
#  Flag=MGF_INPBASE64_4x6
#     If this flag is set, then the input will be base-64, where 4 bytes are
#     loaded 6 bits at a time (i.e. 16 bytes of hash).  Used in Cisco PIX-MD5
#     Thus, only the lowest 24 bits of each part of the MD5 hash are used.
#     This has to happen on the binary loader and comparison functions.
#
#  Flag=MGF_USERNAME
#     If there is a Username used in the format, use this flag.
#
#  Flag=MGF_USERNAME_UPCASE User name, but it is forced to uppercase
#  Flag=MGF_USERNAME_LOCASE User name, but it is forced to lowercase
#
#  Flag=MGF_SALT_AS_HEX
#     This flag is for formats such as md5(md5($p).md5($s))  What this
#     does, is to return the md5($s) to john as the salt, and not simply
#     return the 'original' salt. Thus, we run md5's for the salt at
#     initialization, and never MD5 them again.  DO NOT use this for a
#     format like md5($s.md5($p).md5($s))  In that format, you NEED
#     the salt (along with the md5 of that salt).  This flag can not
#     be used in that instance.
#
#  Flag=MGF_SALT_AS_HEX_TO_SALT2
#     This is 'similar' to MGF_SALT_AS_HEX.  It will perform the
#     base16 crypt of the salt.  However, it stores both the original
#     salt, and creates a 'salt2' which is the base16  This is so a
#     format like,  md5($p.$s.md5($s)) could have both the salt
#     and still have the base-16 of the salt.  So in this case
#     using append_salt would append the original salt, and
#     append_2nd_salt would append the base-16 hash of the salt.
#
#  Flag=MGF_StartInX86Mode
#     This flag will case the format to 'start' in X86 mode, so that
#     any initial key loading will also be done to the X86 buffers.
#
#  Flag=MGF_SALT_UNICODE_B4_CRYPT
#     This flag will case the format to 'start' in Unicode conversion
#     mode, so that any initial key loading will also be done to the
#     using unicode conversions.
#
#  Flag=MGF_BASE_16_OUTPUT_UPCASE
#     This flag will case the format to 'start' in uppercase base-16
#     conversion mode, so that any initial key loading will also be
#     done to the using unicode conversions.
#
#  MGF_FLD0, MGF_FLD1, MGF_FLD2, ... ,MGF_FLD9
#     If any of the fields of the file are part of the format (and
#     used within the script), then they either must be part of
#     the hash value, or the field must be present on the line.
#     If a field IS used, then you also MUST have the proper
#     flag set, or the prepare function will NOT load it.
#
# MGF_INPUT_20_BYTE
#     Tell $dynamic$ that the 'final' crypt is an SHA1 (still experimental
#     code, so 'caveat emptor' ).  What this will do, is cause john to only load
#     hashes that are 20 binary bytes of input (40 hex digits md5 is 32 hex
#     digits). NOTE, when the 'final' crypt is done (in SHA1), it will only
#     have the first 16 bytes compared against (vs the full 20 bytes which
#     sha1 generates).  This 'should' be more than adequate to know
#     you have cracked the hash.
# MGF_INPUT_24_BYTE
# MGF_INPUT_28_BYTE
# MGF_INPUT_32_BYTE
# MGF_INPUT_40_BYTE
# MGF_INPUT_48_BYTE
# MGF_INPUT_64_BYTE
#     These are similar to the 20 byte flag for the SHA1. NOTE, this
#     means that dynamic will only look for valid input strings which
#     are the appropriate length, based upon that final hash size.
#     One off these flags MUST be used, on a format that is longer than
#     the 16 byte MD5 (or md4) hash.
#
# MGF_INPBASE64a
#     The binary input hash data format of the FreeBSD_MD5 and Apache_MD5.
#
# MGF_UTF8
#     This format 'allows' the --encoding=utf-8 switch, and will perform
#     full UTF conversions, if the DynamicFunc__setmode_unicode()
#     function has been issued (or one of the user name or preload
#     password unicode flags were used).
#
#
########################################################
#
#   Function primitives, and what they do  (REQUIRED in each format section)
#
#################
#  DynamicFunc__clean_input
#     Sets lengths of all input1[] array to ZERO, and sets the memory null (memory only set in SSE2, since it does
#     not matter in x86 builds)
#
#  DynamicFunc__clean_input2
#     Same as DynamicFunc__clean_input but this one works on input2[] array.
#
#  DynamicFunc__clean_input_kwik
#     Sets lengths of all input1[] array to ZERO. Memory not cleared.  Optimization that can be used when you KNOW
#     that the length of any of the input1 strings will never be less than they once were.  So if you load keys,
#     crypt, clean_input, then repeatedly append_base16 to input and crypt, then during that repeated set of work
#     you can call clean_kwik since ALL things being crypted, would be 32 bytes, and the single 'deep' clean would
#     get the buffers read to go, and they would not need fully cleaning again.
#
#  DynamicFunc__clean_input2_kwik
#     Same as DynamicFunc__clean_input_kwik but this one works on input2[] array.
#
#  DynamicFunc__clean_input_full
#     This makes sure that the entire keys are set with NULL's prior to running.  This is a full 'deep' clean.
#     In x86 builds, it will fully null out the entire input buffer.  In SSE mode, it nulls out both the
#     x86 and SSE input buffers (fully).
#
#  DynamicFunc__clean_input2_full
#     Same as DynamicFunc__clean_input_kwik but this one works on input2[] array.
#
#  DynamicFunc__append_keys
#     append the array of keys to the array input1[]
#
#  DynamicFunc__append_keys_pad16
#     append the array of keys to the array input1[], padding with nulls to 16 bytes, if input shorter.
#     Needed for net-md5 and net-sha1 formats.
#
#  DynamicFunc__append_keys2
#     append the array of keys to the array input2[]
#
#  DynamicFunc__append_salt
#     Appends the salt to each element of input1[], and adjusts the length of each element.
#     NOTE, the MGF_SALT flag must be used in the format.
#
#  DynamicFunc__append_salt2
#     Appends the salt to each element of input2[], and adjusts the length of each element.
#
#  DynamicFunc__append_2nd_salt
#     append the salt2 (second salt value) to the array input1[]
#     NOTE, the MGF_SALT2 flag must be used in the format.
#
#  DynamicFunc__append_2nd_salt2
#     append the salt2 to the array input2[]
#
#  DynamicFunc__append_userid
#  DynamicFunc__append_userid2
#     appends the username into either input1 or input2 fields.
#
#  DynamicFunc__crypt_md5
#     performs a md5 on all elements of input1[] and places the results into output1[]  The output will be the
#     16 byte BINARY blob of the 'raw' md5.
#
#  DynamicFunc__crypt_md4
#     same as DynamicFunc__crypt_md5, but uses the MD4 hash instead of md5.
#
#  DynamicFunc__crypt2_md5
#     Same as DynamicFunc__crypt_md5 but this one works on input2[] -> output2[] arrays.
#
#  DynamicFunc__crypt2_md4
#     Same as DynamicFunc__crypt2_md4 but uses the MD4 hash instead of md5.
#
#  DynamicFunc__crypt_md5_in1_to_out2
#     Takes the data from input1, performs the MD5 crypts, and stores this data into Output2
#
#  DynamicFunc__crypt_md4_in1_to_out2
#     Takes the data from input1, performs the MD4 crypts, and stores this data into Output2
#
#  DynamicFunc__crypt_md5_in2_to_out1
#     Takes the data from input2, performs the MD5 crypts, and stores this data into Output1
#
#  DynamicFunc__crypt_md4_in2_to_out1
#     Takes the data from input2, performs the MD4 crypts, and stores this data into Output1
#
#  DynamicFunc__append_from_last_output_as_base16
#     Takes the 16 byte binary values from each output1[] array, and does a base-16 conversion, appending the
#     results to the same 'index' of the input1[] array.  ALL of the lengths of the array1[] elements will be
#     32 bytes more than they were (the base-16 of each 16 byte binary value is 32 bytes long).
#
#  DynamicFunc__append_from_last_output2_as_base16
#     Same as DynamicFunc__append_from_last_output_as_base16 but this one works on output2[] -> input2[] arrays.
#
#  DynamicFunc__append_from_last_output_to_input2_as_base16
#     output1[] append -> input2[] arrays.  (note, output #1 appending to input #2, base-16)
#
#  DynamicFunc__append_from_last_output2_to_input1_as_base16
#     output2[] append -> input1[] arrays.  (note, output #2 appending to input #1, base-16)
#
#  DynamicFunc__append_input_from_input2
#     Appends input2 to input   input += input2.  Note, the inputX -> inputY copying in SSE2 is slower than
#     doing output -> input.
#
#  DynamicFunc__append_input2_from_input
#     Appends input1 to input2   input2 += input
#
#  DynamicFunc__append_input_from_input
#     Appends input1 to input1   input1 += input2
#
#  DynamicFunc__append_input2_from_input2
#     Appends input2 to input2   input2 += input2
#
#  DynamicFunc__append_from_last_output2_as_raw
#     Appends the 'raw' output data from output2 to input1.  Length of input1 grows by 16 bytes.
#
#  DynamicFunc__overwrite_from_last_output_as_base16_no_size_fix
#     NOT IMPLEMENTED:
#     Takes the 16 byte binary values from the output1[] array, and overwrites from byte 0 to byte 31 of each
#     element of the input1[] array.  This is used as an optimizations for formats such as:  md5(md5($s).$p)
#     in this format, we can place the keys into input1[] at 32 bytes past the start of the buffer, and set
#     the lengths to be the length of each key + 32, and then simply call this function for each salt, to
#     drop the 32 bytes of md5 data to the start of the strings, without touching the passwords that were
#     already stored, or the lengths of the strings.
#
#  DynamicFunc__overwrite_from_last_output2_as_base16_no_size_fix
#     NOT IMPLEMENTED:
#     Same as DynamicFunc__overwrite_from_last_output_as_base16_no_size_fix but this one works on output2[] -> input2[] arrays.
#
#  DynamicFunc__overwrite_from_last_output_to_input2_as_base16_no_size_fix
#     NOT IMPLEMENTED:
#     output1[] overwrite start of input2[] arrays.  (note, output #1 going to start of input #2, base-16, no size 'fix')
#     like DynamicFunc__overwrite_from_last_output_as_base16_no_size_fix but different input output vars.
#
#  DynamicFunc__overwrite_from_last_output2_to_input1_as_base16_no_size_fix
#     NOT IMPLEMENTED:
#     output2[] overwrite start of input1[] arrays.  (note, output #2 going to start of input #1, base-16, no size 'fix')
#     like DynamicFunc__overwrite_from_last_output_as_base16_no_size_fix but different input output vars.
#
#  DynamicFunc__overwrite_salt_to_input1_no_size_fix
#     Inserts the salt to the start of the first input buffer.  DOES NOT append null bytes, nor does it
#     adjust the length of the strings.  Used if the 'top' part of input buffer 1 stays constant, and
#     the salt gets inserted to the start of input buffer (but lengths never change).
#
#  DynamicFunc__overwrite_salt_to_input2_no_size_fix
#     Same as DynamicFunc__overwrite_salt_to_input1_no_size_fix but this one works on salt -> input2[] arrays.
#
#  DynamicFunc__append2_from_last_output2_as_raw
#  NOT IMPLEMENTED
#  DynamicFunc__append_from_last_output1_as_raw
#  NOT IMPLEMENTED
#  DynamicFunc__append2_from_last_output1_as_raw
#  NOT IMPLEMENTED
#
#  DynamicFunc__set_input_len_16
#     Sets the length of each element of input1[] to 16.  In SSE2 mode, if the length of the input buffer
#     was less than 16, then that 0x80 end of marker is removed, and a new one is 'added' at offset 16.
#
#  DynamicFunc__set_input_len_20
#     Sets the length of each element of input1[] to 20.  In SSE2 mode, if the length of the input buffer
#     was less than 20, then that 0x80 end of marker is removed, and a new one is 'added' at offset 20.
#
#  DynamicFunc__set_input2_len_16
#     Sets the length of each element of input2[] to 16.
#
#  DynamicFunc__set_input2_len_20
#     Sets the length of each element of input2[] to 20.
#
#  DynamicFunc__set_input_len_32
#     Sets the length of each element of input1[] to 32.  This was designed as is used in conjunction with
#     the funtion overwrite_from_last_output_as_base16_no_size_fix in mind, but would not
#     be the 'only' reason to use it (but is the only reason I have at this time).  This function does NOT
#     clean the input[] items prior to this.  Usually a call to clean_input would be required prior to
#     calling this function, to make sure the buffer is 'clean'.  A 0x80 is placed into offset 32
#
#  DynamicFunc__set_input2_len_32
#     Same as DynamicFunc__set_input_len_32 but this one works on input2[] array.
#
#  DynamicFunc__set_input_len_40
#     Sets the length of each element of input1[] to 30.  A 0x80 is placed into offset 40.  This would be
#     the companion function to DynamicFunc__set_input_len_32, but useful for SHA1 hashes which would
#     themselves output 40 bytes of hex.
#
#  DynamicFunc__set_input2_len_40
#     Same as DynamicFunc__set_input_len_40 but this one works on input2[] array.
#
#  DynamicFunc__set_input_len_64
#     Like the above function, but sets lengths to 64 bytes.  NOTE valid to use in SSE2 mode.
#
#  DynamicFunc__set_input2_len_64
#     Same as DynamicFunc__set_input2_len_64 but this one works on input2[] array.
#
#  DynamicFunc__set_input_len_100
#     Like the above function, but sets lengths to 100 bytes.  NOTE valid to use in SSE2 mode.
#     This is a 'specialized' function for the RAdmin hash, and simply nulls out the buffer
#     up to 100 bytes.
#
#  SSE 'switching' functions.  SSE is only valid for 54 byte or less input strings.
#  This is due to optimizations which place a 'single' md5 limb restriction on the
#  crypts.  Thus, for some formats, we have to be in x86 mode only, but for others,
#  we can jump in and out of SSE mode.  However, to do this, we likely will have
#  to convert a buffer (or more).  The input buffers, and output buffers are NOT
#  compatible between x86 and SSE2.  If possible, convert the output buffers, they
#  are smaller, and faster (usually).
#
#  NOTE, proper usage of these functions CAN improve performance of some SHA1 processing
#  putting the data into or out of 'flat' model. The SHA1 hash does have SSE code, however
#  since it also requires BE layout for the data (vs LE for md5), a design choice was made
#  to not store the BE sse buffers long term.  All crypting will determin IF sse sha1 code
#  should be used, and if so, the crypt function will convert from flat into sse format.
#  however, if the data already is in sse format, it has to be transformed into a format
#  valid for ShA1.   Look at formats 1022 to 1031 for insite on how to best use these
#  data conversion functions with SHA1.
#
#  DynamicFunc__SSEtoX86_switch_input1
#    Copies the SSE Input1 buffer to x86 Input1.  Changes 'mode' to non-SSE for any
#    functions that are done after this call.
#  DynamicFunc__SSEtoX86_switch_input2
#    Copies the SSE Input2 buffer to x86 Input2.  and changes to non-SSE mode.
#  DynamicFunc__SSEtoX86_switch_output1
#    Copies the SSE Output1 buffer to x86 Output1.  and changes to non-SSE mode.
#  DynamicFunc__SSEtoX86_switch_output2
#    Copies the SSE Output2 buffer to x86 Output2.  and changes to non-SSE mode.
#
#  DynamicFunc__X86toSSE_switch_input1
#    Copies the x86 Input1 buffer to SSE Input1.  Changes 'mode' to SSE for any
#    functions that are done after this call.
#  DynamicFunc__X86toSSE_switch_input2
#    Copies the x86 Input2 buffer to SSE Input2.  and changes to SSE mode.
#  DynamicFunc__X86toSSE_switch_output1
#    Copies the x86 Output1 buffer to SSE Output1.  and changes to SSE mode.
#  DynamicFunc__X86toSSE_switch_output2
#    Copies the x86 Output2 buffer to SSE Output2.  and changes to SSE mode.
#
#  DynamicFunc__ToSSE
#    Simply switches mode to SSE. No data buffers copied. Usually, used as first
#    or last command in a script
#  DynamicFunc__ToX86
#    Simply switches mode to x86. No data buffers copied. Usually, used as first
#    or last command in a script
#
#
#  Unicode mode.  This allows $dynamic$ to do unicode coversion when doing string
#  append (overwrite) operations.  Thus, a format such as md5(unicode($p).$s) can
#  be generated.
#
#  DynamicFunc__setmode_unicode
#    All string ops do Unicode conversions. These are DynamicFunc__append_keys,
#    append_keys2, append_salt, append_salt2, overwrite_salt_to_input1_no_size_fix,
#    overwrite_salt_to_input2_no_size_fix, append_2nd_salt, append_2nd_salt2,
#    append_userid, append_userid2, append_input1_from_CONST1  (and ALL other const
#    append functions)  If --encoding=utf-8 switch is set, then a utf-8 to unicode
#    conversion is done.  If that switch was not used on the command line, then a
#    simple ascii to unicode (which is simply appending a null byte after each byte
#    of string).  The --encoding=utf-8 conversion is considerably slower, but is required
#    for many Unicode or UTF-8 systems.  NOTE, to honor the --encoding=utf-8 switch, the
#    format MUST use the MGF_UTF8 flag.
#
#  DynamicFunc__setmode_normal
#    This is the 'normal' run for john.  Back to ascii (or utf-8, if --encoding=utf-8
#    command line switch was used).  But there will be NO Unicode conversions done.
#
#
#  Upper/Lowercase of md5 - base16 conversions.  Normally, this is done to lower
#  case.  However, there may be systems which do md5 to base 16 in upper case.
#  thus, if a hash is md5(upper(md5($p))  this is different than md5(md5($p))
#  These functions turn on or off the upper/lower case conversions.
#
#  DynamicFunc__base16_convert_locase
#    This is the 'normal' lowercase. Thus, md5(md5($p)) is assumed to have the
#    base-16 done in lower case.
#
#  DynamicFunc__base16_convert_upcase
#    Perform base-16 converts in upper case base-16.
#
#  DynamicFunc__append_input1_from_CONST1
#     This will append the first constant value to the input1[] array
#  DynamicFunc__append_input1_from_CONST2
#  DynamicFunc__append_input1_from_CONST3
#  DynamicFunc__append_input1_from_CONST4
#  DynamicFunc__append_input1_from_CONST5
#  DynamicFunc__append_input1_from_CONST6
#  DynamicFunc__append_input1_from_CONST7
#  DynamicFunc__append_input1_from_CONST8
#     Same as DynamicFunc__append_input1_from_CONST1 but they load the other constant
#     values into input1.
#
#  DynamicFunc__append_input2_from_CONST1
#     This loads the first constant value into the input2[] array
#  DynamicFunc__append_input2_from_CONST2
#  DynamicFunc__append_input2_from_CONST3
#  DynamicFunc__append_input2_from_CONST4
#  DynamicFunc__append_input2_from_CONST5
#  DynamicFunc__append_input2_from_CONST6
#  DynamicFunc__append_input2_from_CONST7
#  DynamicFunc__append_input2_from_CONST8
#     Same as DynamicFunc__append_input2_from_CONST1 but they load the other constant
#     values into input2.
#
#  DynamicFunc__append_fld0
#     This will append the data from $$F0 (field 0 of the line, or 'username', to input 1.
#  DynamicFunc__append_fld1
#  DynamicFunc__append_fld2
#  ....
#  DynamicFunc__append_fld9
#     Will append data from the other fields (from 1 to 9).  NOTE, 1 is the 'hash' line, and
#     almost certainly should NOT be used.
#
#  DynamicFunc__append2_fld0  -- to -- DynamicFunc__append2_fld9
#     Loads data from $$F0 to $$F9 (field 0 to field 9) into input buffer 2.
#
# DynamicFunc__SHA1_crypt_input1_append_input2_base16    **
# DynamicFunc__SHA1_crypt_input2_append_input1_base16    **
# DynamicFunc__SHA1_crypt_input1_overwrite_input1_base16 **
# DynamicFunc__SHA1_crypt_input2_overwrite_input2_base16 **
# DynamicFunc__SHA1_crypt_input1_overwrite_input2_base16 **
# DynamicFunc__SHA1_crypt_input2_overwrite_input1_base16 **
# DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
# DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
#    Currently 'experimental' addition of SHA1 into the md5() syntax.  Still much work to
#    do.  Not for general use (yet). Hopefully, will be able to get things like
#    md5(sha1(md5($p))) working (sort of works now, but again, still experimental).
#    The 2 '_FINAL' functions should ONLY be used as the last call. This allows
#    for support of sha1(sha1(m5($p).$s)) type hashes.  NOTE, if you use on of the
#    *_FINAL calls, then you have to use the MGF_INPUT_20_BYTE flag, and notes
#    that only the first 16 bytes of the final hash are evaluated. Also, only 40 bytes
#    long hex string hashes will be loaded (base-64? needed?).
#    At this time, ALL sha1 is done in non-SSE code (x86, OpenSSL). Also note, that
#    ALL output from the SHA1 functions is directly appended or overwritten, right into
#    an input buffer.
#
#    **  NOTE, these functions are depricated.  They should not be used any more.
#        The proper usage would be to set the output format (no setting is needed
#        IF base-16 is wanted, as that will be default), and then to call the same
#        named function primate, but that function name is minus the _base16 part.
#        so DynamicFunc__SHA1_crypt_input1_append_input2 would be the proper function,
#        instead of the depricated DynamicFunc__SHA1_crypt_input1_append_input2_base16
#
####################
#
# OTHER large hash functions (SHA2 hashes, GOST, WHIRLPOOL, Tiger, RIPEMD(128/160/256/512) currently)
#
####################
#
# These other large hash types, have been built like the SHA1 (listed above). They will have
# the same 8 function primitives, and be named exactly the same as the SHA1, except they
# will have the crypt in the function name.  The current hashes supported are:
#     SHA224, SHA256, SHA384, SHA512, GOST, WHIRLPOOL.
#     NOTE Whirlpool is ONLY supported in new versions of OpenSSL, so if the version of oSSL
#     is not new enough, the whirlpool part of dynamic will NOT be compiled into JtR.
#
# Here is an example.  Listed will be the functions for SHA256.  NOTE all of the other types
# will be exactly the same, just using different crypt values in the function names.
#
# DynamicFunc__SHA256_crypt_input1_append_input2
# DynamicFunc__SHA256_crypt_input2_append_input1
# DynamicFunc__SHA256_crypt_input1_overwrite_input1
# DynamicFunc__SHA256_crypt_input2_overwrite_input2
# DynamicFunc__SHA256_crypt_input1_overwrite_input2
# DynamicFunc__SHA256_crypt_input2_overwrite_input1
# DynamicFunc__SHA256_crypt_input1_to_output1_FINAL
# DynamicFunc__SHA256_crypt_input2_to_output1_FINAL
#
# All of these 'large' hash functions, when used MUST end with either an MD5 or MD4 into
# output1, OR by using one of the *_to_output1_FINAL calls.  The ONLY call actaully
# has to be one of the *_to_output1_FINAL calls (if the format is a simple single
# hash, such as SHA512($s.$p.$u)  In this case, we would simply load one of the input
# variables with the salt, the password, and the userid, and then call a single
# DynamicFunc__SHA256_crypt_input1_to_output1_FINAL to perform the crypt.
#
####################
# Controlling the output of 'large' crypts.
####################
#
# All of the 'large' crypts, have simple function primitives. These will perform the
# crypt, and then write output to one of the 2 buffers. The output can be appeneded,
# or overwritten.  The format this output is done in CAN be controlled. The output
# formats are:  base-16  (0-9 and a-f), base-16 upcase (0-9 and A-F), base-64 (MIME)
# base-64 with no 'trailing' '=' characters (the '=' is normally inserted for strings
# that are not evenly divisible by 3). Also, 'RAW' hash value can be output.
#
# These are the 'helper' functions that affect large hash output.  Every call to crypt_all
# will set base-16 to be the default output format.  These helper functions change this,
# and once an output format it set, it 'stays' in that format, until set by another
# helper, or at the next call to crypt_all (i.e. the format starts over again)
#
# DynamicFunc__LargeHash_OUTMode_base16
# DynamicFunc__LargeHash_OUTMode_base16u
# DynamicFunc__LargeHash_OUTMode_base64
# DynamicFunc__LargeHash_OUTMode_base64_nte
# DynamicFunc__LargeHash_OUTMode_raw
#
####################
#  These are special functions written SPECIFCALLY for usage by phpass code, in builtin
#  function dynamic_17.  These are likely NOT useful.  However, they 'are' open to be used.
####################
#
#  DynamicFunc__crypt_md5_to_input_raw
#     Used as 'first' step of phpass crypt.  it will put 16 binary bytes of md5 encryption
#     into array1[].  It will write this to the beginning of the arrays (does not append), and
#     when done, sets ALL lengths of array1[] to 16.  Once this function is called, the keys
#     are appended (see spec for phpass encryption), which sets the length of input1[] elements
#     to 16 + strlen(keys).  Then there is a loop that calls the next function.
#
#  DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#     This function will encrypt input1[] and drop the 16 bytes of MD5 binary right back over
#     the first 16 bytes of input1[]  The length of each input1[] item is NOT touched, thus
#     whatever the length was before is what it is now.
#
#  DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen_but_setlen_in_SSE
#     This is the very first call.  It DOES set the length up properly. From that point on,
#     the other (1000's) of calls are done using DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#
#  DynamicFunc__PHPassCrypt
#     Special function for phpass, probably not useful in john.conf scripting
#
#  DynamicFunc__POCrypt
#     This is the 'special' crypt call for MD5-PO dynamic_18.  It was done as a 'special', because
#     there was about 20% overhead, doing the individual calls.  It was much faster to hammer in
#     values in a special crypt call.
#
####################################################################
#
#   Test= line(s)   (REQUIRED in each format section)
#
#    NOTE within these lines, if there are binary values which need to
#    be inserted, then use the format of \xHH  (2 hex digits which will
#    be converted into the 1 byte).  So, \xF7 will output the single
#    byte "\xF7" which in some ASCII formats, it is the squiggly
#    'about' equals sign.  To output some UNICODE or 'wide' type
#    characters, you will need to know the exact bytes that are used
#    and place a \xHH for each byte.  To output the actual \ character
#    use \\   So, to output \\ use \\\\  (2 \\ which each get replaced
#    with a single \ character).
#    NOTE that HH MUST be uppercased for the A to F values, and the 'x'
#    MUST be lowercase.
#    NOTE, you MUST use \xHH.  So, to put an ASCII(1) value, you would
#    use \x01  (not \x1 ).
#    ALL conversions are done prior to the line being 'looked at' within
#    the parser.
#
#    There are other specialized 'Test=' lines that may need to be used
#    in certain situations.
#
#  TestU= allows placing a test case out, that will ONLY load if the encoding
#    of utf-8 is selected from command line.
#
#  TestA= allows placing a test case will will NEVER be loaded if encoding of
#    utf-8 is selected at command line (ASCII test case).
#
#  TestM= Allows placing a test case that will ONLY load if this is an MMX or
#    SSE build.
#
#  TestD= Allows placing a test case that will ONLY load, if -DDEBUG build of
#    JtR is done (for testing).  At this time, the REALLY long password hashes
#    are being added with TestD=  so that the timing of the formats will not be
#    impacted, but so that a debugging build can be done, and the extra long
#    test strings will still be there and be tested.
#
#  TestF= Allows placing a test case that will NEVER be loaded if this was an
#    MMX or SSE build (Only load for 'flat' builds, like oSSL only).
#
#################
#
# The testing line is simply of this format:
#
#  test=$dynamic_#$hash[$salt]:password
#
#  the hash listed, MUST be the hash generated for this password (and
#  salt) for the expression.  John uses this at startup to validate
#  that the format is 'valid'.  If this Test= line(s) are not right
#  john will not work.  NOTE if the format does not allow a colon
#  in the format (such as dynamic_1007, then the exact same field
#  separator must be used in the Test= line, as what is specified
#  in the --field-separator-char=c used on the command line
#
####################################################################
#
#   Expression= line   (Optional)
#
#################
#
#  If the Expression= line is there, then the value shown there will
#  also be used on the startup line of john.  If missing, john will
#  output dynamic_1001  (or whatever number was used), and if
#  Expression=, then the dynamic_1001 will be followed by the
#  expression line.
#
####################################################################
#
#   SaltLen=# line   (Optional, but required IF the format needs it)
#
#################
#
#  If this is a salted hash, and ALL valid salts must be a specific
#  length (vBulletin is an example, where 3 byte salts are specified
#  and OSCommerce is another with 2 byte hashes).  If this is the
#  case, add a SaltLen=# line and john will validate, and only test
#  lines which have valid salts (i.e. right length salt is part of
#  the initial validation of the line)
#
####################################################################
#
#   SaltLenX86=# line   (Optional, but required IF the format needs it)
#
#################
#
#  This is almost never needed.  But if needed to have a different
#  salt size on non-MMX/SSE builds, then this is how to do it.
#
####################################################################
#
#   MaxInputLen=# line   (Optional, but required IF the format needs it)
#
#################
#
#  If a format has a 'max' length of password (such as Cisco PIX has
#  a max length of 16 byte PW), then this must be set.  If this is
#  not set, then the format will have a max password allowed to be
#  55-abs(saltlen)  (80-abs(saltlen) if not an SSE/MMX build)
#
####################################################################
#
#   MaxInputLenX86=# line   (Optional, but required IF the format needs it)
#
#################
#
#  Similar to the SaltLenX86, in that it allows a different password
#  length to be force set in non-SSE builds.  So if a format should
#  ONLY have 16 byte passwords, and no salt, then you would need
#  to set MaxInputLen=16  and have a MaxInputLenX86=16 set also.
#
####################################################################
#
#   CONST#=str    line   (Optional, but required IF the format needs it)
#
#   These lines will also use the \xHH  (or \\ for a \ ) same as the
#   'Test=' lines.
#
#################
#
#  This will allow a constant value to be 'set' and then later used
#  in the format.  So a format such as md5($p.CONSTANT.$s) can be
#  done by setting const 1 to 'CONSTANT' (whatever that string is)
#  and then calling DynamicFunc__append_input1_from_CONST1 at the
#  proper time to insert it into the string.
#  CONST1=str   to CONST8=str   are valid (8 constant values.)  NOTE
#  if you have 3 constants, then use CONST1, CONST2 and CONST3 (i.e.
#  pack them into the first X number constants).
#
####################################################################



####################################################################
####################################################################
####################################################################
#  Here are some additional 'test' formats.  To use these, simply
#  cut them and place them under a new name at the bottom of
#  dynamic.conf.
#  These require john-1.7.7 jumbo2 + to work properly
####################################################################
####################################################################
####################################################################


####################################################################
# Simple DYNAMIC type for PostOffice-MD5 md5($s.Y.$p.\xF7.$s)
#    Included here to 'exercise' the script parser (constants)
#    NOTE, the internal dynamic_18 has a specialized crypt
#          function.  It does the same as what this script does
#          but does not have the overhead of this script work, and
#          runs 10-15% faster.
####################################################################
[List.Generic:dynamic_1009]
Expression=dynamic_1009: md5($s.Y.$p.\xF7.$s) [Post.Office MD5]
Flag=MGF_SALTED
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_input1_from_CONST2
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1009$550c41c11bab48f9dbd8203ed313eef0$aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa:abc123
Test=$dynamic_1009$0c78bdef7d5448105cfbbc9aaa490a44$550c41c11bab48f9dbd8203ed313eef0:abc123
Test=$dynamic_1009$9be296cf73d2f548dae3cccafaff1dd9$82916963c701200625cba2acd40d6569:FRED
Test=$dynamic_1009$a0e2078f0354846ec5bc4c7d7be08a46$82916963c701200625cba2acd40d6569:
CONST1=Y
CONST2=\xF7
SaltLen=32

####################################################################
# Simple DYNAMIC type for Cisco PIX hash (same as pixMD5_fmt.c)
####################################################################
[List.Generic:dynamic_1010]
Expression=dynamic_1010: pixMD5
Flag=MGF_INPBASE64_4x6
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__set_input_len_16
Func=DynamicFunc__crypt_md5
Test=$dynamic_1010$2KFQnbNIdI.2KYOU:cisco
Test=$dynamic_1010$TRPEas6f/aa6JSPL:test1
Test=$dynamic_1010$OMT6mXmAvGyzrCtp:test2
Test=$dynamic_1010$gTC7RIy1XJzagmLm:test3
Test=$dynamic_1010$oWC1WRwqlBlbpf/O:test4
Test=$dynamic_1010$NuLKvvWGg.x9HEKO:password
Test=$dynamic_1010$8Ry2YjIyt7RRXU24:
Test=$dynamic_1010$.7nfVBEIEu4KbF/1:0123456789abcdef
MaxInputLen=16

####################################################################
# Simple DYNAMIC type for Cisco PIX hash with a salt.  The salt
# is the first 4 bytes of the user name.  I have made an assumption
# about PW len. I assume it maxes at 12 chars, so the full salt is
# allowed.
####################################################################
[List.Generic:dynamic_1011]
Expression=dynamic_1011: pixMD5-salted
Flag=MGF_INPBASE64_4x6
Flag=MGF_SALTED
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__set_input_len_16
Func=DynamicFunc__crypt_md5
Test=$dynamic_1011$h3mJrcH0901pqX/m$alex:ripper
Test=$dynamic_1011$3USUcOPFUiMCO4Jk$cisc:cisco
Test=$dynamic_1011$lZt7HSIXw3.QP7.R$admc:CscFw-ITC!
Test=$dynamic_1011$hN7LzeyYjw12FSIU$john:cisco
Test=$dynamic_1011$7DrfeZ7cyOj/PslD$jack:cisco
MaxInputLen=12
SaltLen=4

####################################################################
# Simple DYNAMIC type for PostOffice-MD5 md5($s.Y.$p.\xF7.$s)
#    Included here to 'exercise' the script parser (constants)
#    NOTE, the internal dynamic_18 has a specialized crypt
#          function, as does this version.  This is same as dynamic_18
####################################################################
[List.Generic:dynamic_1012]
Expression=dynamic_1012: md5($s.Y.$p.\xF7.$s) [Post.Office MD5]
Flag=MGF_SALTED
Flag=MGF_NOTSSE2Safe
Flag=MGF_POSetup
Func=DynamicFunc__POCrypt
Test=$dynamic_1012$550c41c11bab48f9dbd8203ed313eef0$aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa:abc123
Test=$dynamic_1012$0c78bdef7d5448105cfbbc9aaa490a44$550c41c11bab48f9dbd8203ed313eef0:abc123
Test=$dynamic_1012$9be296cf73d2f548dae3cccafaff1dd9$82916963c701200625cba2acd40d6569:FRED
Test=$dynamic_1012$a0e2078f0354846ec5bc4c7d7be08a46$82916963c701200625cba2acd40d6569:
Test=$dynamic_1012$eddfe6493d7e692994cf3526c71f8c84$aabbccddeeffgghhlksjflksjlfkdjsl:test1
SaltLen=32

####################################################################
# Simple DYNAMIC type for phpass-MD5.  Same as dynamic_17
####################################################################
[List.Generic:dynamic_1013]
Expression=dynamic_1013: phpass ($P$ or $H$)
Flag=MGF_SALTED
Flag=MGF_PHPassSetup
Flag=MGF_INPBASE64
Func=DynamicFunc__PHPassCrypt
Test=$dynamic_1013$jgypwqm.JsMssPLiS8YQ00$9aaaaaSXB:test1
Test=$dynamic_1013$5R3ueot5zwV.7MyzAItyg/$912345678:thatsworking
Test=$dynamic_1013$yhzfTrJXcouTpLleLSbnY/$7aaaaaSXB:test1
Test=$dynamic_1013$mwulIMWPGe6RPXG1/R8l50$712345678:thatsworking
Test=$dynamic_1013$nfKm8qpXa88RVUjLgjY/u1$7555555hh:test3
Test=$dynamic_1013$JSe8S8ufpLrsNE7utOpWc/$BaaaaaSXB:test1
Test=$dynamic_1013$aqkw6carDzQ67zCLOvVp60$B12345678:thatsworking
Test=$dynamic_1013$o2j.1E7X1NvVyEJ/QY9hX0$B555555hh:test3
Test=$dynamic_1013$Y5RwgMij0xFsUIrr33lM1/$9555555hh:test3
SaltLen=9


####################################################################
# Simple DYNAMIC type for phpass-MD5.  Same as dynamic_17
#  This format shows what happens under the hood, INSIDE of the
#  DynamicFunc__PHPassCrypt() function.  We ONLY do $P$7, since
#  it is the 'smallest' of the set. To do this WITHOUT the
#  DynamicFunc__PHPassCrypt() function, we would have to have a
#  special format for EACH type.  NOTE the timings will 'appear' to
#  be MUCH faster than normal phpass. This is due to ONLY checking
#  a version of phpass that uses 512 crypts.  In the phpass testing
#  we select a version that tests 2048 of them.  So in other words
#  if this format was same 'speed', it would be 4x faster.  NOTE it
#  is a little slower than that, due to added overhead.
####################################################################
[List.Generic:dynamic_1014]
Expression=dynamic_1014: phpass(512 crypt version) ($P$7 or $H$7 )
Flag=MGF_SALTED
Flag=MGF_INPBASE64
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5_to_input_raw
Func=DynamicFunc__append_keys
# for type 7, we perform 512 crypts
# 1 crypt here
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen_but_setlen_in_SSE
# Now, perform 512 crypts
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#8 done
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#16 done
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#32 done
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#64 done
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#128 done
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#256 done
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#Func=DynamicFunc__crypt_md5_to_input_raw_Overwrite_NoLen
#512 done, but we did not test last 2
# last crypt (makes 512 crypts TOTAL).
# This will FAIL for big endian machines.  We have special code
# inside our phpass to correctly deal with this.  In this format
# there is NO way to do that.  Sorry, It simply can not be done
# right now on BE systems.
Func=DynamicFunc__crypt_md5
Test=$dynamic_1014$yhzfTrJXcouTpLleLSbnY/$aaaaaSXB:test1
Test=$dynamic_1014$mwulIMWPGe6RPXG1/R8l50$12345678:thatsworking
Test=$dynamic_1014$nfKm8qpXa88RVUjLgjY/u1$555555hh:test3
SaltLen=8

[List.Generic:dynamic_1015]
# same as 15, the user id format
Expression=md5($u.md5($p).$s)
Flag=MGF_SALTED
Flag=MGF_USERNAME
Flag=MGF_KEYS_CRYPT_IN2
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1015$6093d5cb3e2f99d9110eb9c4bbca5f8c$aaaSXB$$Ujoeblow:test1
Test=$dynamic_1015$6a2dc4a6637bc5c2488f27faeead8720$123456$$Uadmin:thatsworking
Test=$dynamic_1015$63aea4b8fe491df8545cc0099ac668d4$5555hh$$Uralph:test3
# to test 'like' we would see from an 'input file' where user name would be first field
Test=$dynamic_1015$6093d5cb3e2f99d9110eb9c4bbca5f8c$aaaSXB:test1:joeblow
Test=$dynamic_1015$6a2dc4a6637bc5c2488f27faeead8720$123456:thatsworking:admin
Test=$dynamic_1015$63aea4b8fe491df8545cc0099ac668d4$5555hh:test3:ralph


####################################################################
# md5(upper(md5($p)))   Included here to 'exercise' the script parser
####################################################################
[List.Generic:dynamic_1016]
Expression=md5(upper(md5($p)))
# Flag needed here, is Salt.  There is no 'fixed' saltlen.
Flag=MGF_KEYS_INPUT
Flag=MGF_BASE_16_OUTPUT_UPCASE
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1016$42b01e6aebac08b1d481990c77e93206:test1
Test=$dynamic_1016$11d382887431b076d540c4d3e37fe89a:Thatsworking
Test=$dynamic_1016$9ed1f40dffbb84dadc33138958e927b2:test3

[List.Generic:dynamic_1017]
Expression=md5(upper(md5(upper(md5($p)))))
# Flag needed here, is Salt.  There is no 'fixed' saltlen.
Flag=MGF_KEYS_INPUT
Flag=MGF_BASE_16_OUTPUT_UPCASE
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1017$9D55D308E01806EF0227CF5A3AC90F4E:test1
Test=$dynamic_1017$1E80483A2F51520646542CCDC657E476:Thatsworking
Test=$dynamic_1017$0E8851B6B060F9A5A101F540C3E3D669:test3

[List.Generic:dynamic_1018]
Expression=md5(upper(md5(upper(md5(upper(md5($p)))))))
# Flag needed here, is Salt.  There is no 'fixed' saltlen.
Flag=MGF_KEYS_INPUT
Flag=MGF_BASE_16_OUTPUT_UPCASE
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1018$CDA798A1D3B4ED11BCA6C0A3D9747B9A:test1
Test=$dynamic_1018$98D346A7C2079BC2D6F49882E4847CDB:Thatsworking
Test=$dynamic_1018$82262EE9D6DF9E1138F602737A9F897E:test3


[List.Generic:dynamic_1021]
Expression=md5(md5(md5(md5($p))))
# here is the optimized 'script' to perform the md5 4 times on itself
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
Test=$dynamic_1021$57200e13b490d4ae47d5e19be026b057:test1
Test=$dynamic_1021$c6cc44f9e7fb7efcde62ba2e627a49c6:thatsworking
Test=$dynamic_1021$0ae9549604e539a249c1fa9f5e5fb73b:test3

[List.Generic:dynamic_1022]
# expression shown will be the string:   dynamic_1022 md5(md5($s.md5($p.md5($p.md5($s.md5($p.$p).$s)))).md5($s.md5($p.md5($p.md5($s.$p)))).md5($s.md5($s.md5($p.md5($p.$s)))))
Expression=md5(md5($s.md5($p.md5($p.md5($s.md5($p.$p).$s)))).md5($s.md5($p.md5($p.md5($s.$p)))).md5($s.md5($s.md5($p.md5($p.$s)))))
Flag=MGF_SALTED
#
#md5(md5($s.md5($p.md5($p.md5($s.C1.$s)))).md5($s.md5($p.md5($p.md5($s.$p)))).md5($s.md5($s.md5($p.md5($p.$s)))))
#C1=md5($p.$p)
#md5(md5($s.md5($p.md5($p.C2))).md5($s.md5($p.md5($p.md5($s.$p)))).md5($s.md5($s.md5($p.md5($p.$s)))))
#C2=md5($s.C1.$s) (32+len_s2)
#md5(md5($s.md5($p.C3)).md5($s.md5($p.md5($p.md5($s.$p)))).md5($s.md5($s.md5($p.md5($p.$s)))))
#C3 = md5($p.C2)
#md5(md5($s.C4).md5($s.md5($p.md5($p.md5($s.$p)))).md5($s.md5($s.md5($p.md5($p.$s)))))
#C4 = md5($p.C3)
#md5(C5.md5($s.md5($p.md5($p.md5($s.$p)))).md5($s.md5($s.md5($p.md5($p.$s)))))
#C5 = md5($s.C4)
#md5(C5.md5($s.md5($p.md5($p.C6))).md5($s.md5($s.md5($p.md5($p.$s)))))
#C6=md5($s.$p)
#md5(C5.md5($s.md5($p.C7)).md5($s.md5($s.md5($p.md5($p.$s)))))
#C7=md5($p.C6)
#md5(C5.md5($s.C8).md5($s.md5($s.md5($p.md5($p.$s)))))
#C8=md5($p.C7)
#md5(C5.C9.md5($s.md5($s.md5($p.md5($p.$s)))))
#C9=md5($s.C8)
#md5(C5.C9.md5($s.md5($s.md5($p.C10))))
#C10=md5($p.$s)
#md5(C5.C9.md5($s.md5($s.C11)))
#C11=md5($p.C11)
#md5(C5.C9.md5($s.C12))
#C12=md5($s.C11)
#md5(C5.C9.C13)
#C13=md5($s.C12)
#C5.C9.C13 is NOT SSE safe.
#
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
#C1 in Out    Inp, Inp2, Out2 free
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
#C2 in Out    Inp, Inp2, Out2 free
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
#C3 in Out    Inp, Inp2, Out2 free
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
#C4 in Out    Inp, Inp2, Out2 free
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
#C5 in Out    Inp, Inp2, Out2 free

Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
##Func=DynamicFunc__SSEtoX86_switch_input1
#C5 in Inp[x86]     Out, Inp2, Out2 free
Func=DynamicFunc__ToSSE

Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
##Func=DynamicFunc__SSEtoX86_switch_input2
Func=DynamicFunc__append_keys2
##Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt2_md5

#C5 in Inp - C6 in Out2   Out, Inp2 free
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
#C5 in Inp - C7 in Out2   Out, Inp2 free
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
#C5 in Inp - C8 in Out2   Out, Inp2 free
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
#C5 in Inp - C9 in Out2   Out, Inp2 free

Func=DynamicFunc__SSEtoX86_switch_output2
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
#C5.C9 in Inp[x86]   Out, Inp2 Out2 free
Func=DynamicFunc__ToSSE

Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__crypt2_md5
#C5.C9 in Inp - C10 in Out2   Out, Inp2 free
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
#C5.C9 in Inp - C11 in Out2   Out, Inp2 free
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
#C5.C9 in Inp - C12 in Out2   Out, Inp2 free
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
#C5.C9 in Inp - C13 in Out2   Out, Inp2 free

Func=DynamicFunc__SSEtoX86_switch_output2
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
#C5.C9.C13 Inp[x86]          Out, Inp2, Out2 free
Func=DynamicFunc__crypt_md5
#Final answer in Out    Inp, Inp2, Out2 free
Func=DynamicFunc__X86toSSE_switch_output1


Test=$dynamic_1022$c901de937291c6232e789d70dde2cd7f$aabbccdd:test1


####################################################################
# mkmd5gen generated DYNAMIC type for md5(md5($p).md5($p.md5($p.$s).md5($s.$p)))
####################################################################
[List.Generic:dynamic_1023]
Expression=dynamic_1023: md5(md5($p).md5($p.md5($p.$s).md5($s.$p)))
Flag=MGF_SALTED
Flag=MGF_NOTSSE2Safe
#
#md5(md5($p).md5($p.md5($p.$s).md5($s.$p)))
#md5(C1.md5($p.md5($p.$s).md5($s.$p)))
#md5(C1.md5($p.C2.md5($s.$p)))
#md5(C1.md5($p.C2.C3))
#md5(C1.C4)
#
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
#C1 in Out    Inp, Inp2, Out2 free
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
#C1 in Inp    Out, Inp2 Out2 free
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__crypt2_md5
#C1 in Inp C2 in Out2    Free: Out, Inp2
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt_md5_in2_to_out1
#C1 in Inp C2 in Out2 C3 in Out1    Free: Inp2
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt2_md5
#C1 in Inp C4 in Out2 Free: Out1, Inp2
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
#C1.C4 in Inp  Free: Out1, Inp2 Out2
Func=DynamicFunc__crypt_md5
#Result in Out1
#
Test=$dynamic_1023$529f4a3600b1ed4af606a5fbc80f58ee$aabbccdd:test1
Test=$dynamic_1023$aa8dbd8512be7a045ba602ddc2cab64b$zzyyxxww:test3
Test=$dynamic_1023$bd0d60add4315f6d0b13c63226d7111e$01234567:ThisisWorking
Test=$dynamic_1023$7174c2398175d2cb1d86e463cb6c8edd$98765432:abc
Test=$dynamic_1023$cef72db811843bd37f1121f714c32752$65432198:Longer Pass Here
Test=$dynamic_1023$8b0131c839db57eb6410ad84076f2fa2$32165498:Junk
Test=$dynamic_1023$736d738772ae81411d96f56d0edc95c9$74185296:Junk1
Test=$dynamic_1023$d66166897605b76f276e76b5596834f3$96325874:Junky3
SaltLen=8


####################################################################
# mkmd5gen generated DYNAMIC type for md5(md5($p).md5($p.md5($p.$s).md5($s.$p).md5($p.$p)))

####################################################################
# mkmd5gen generated DYNAMIC type for md5($p)
####################################################################
[List.Generic:dynamic_1024]
Expression=dynamic_1024: md5($p)
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1024$5a105e8b9d40e1329780d62ea2265d8a:test1
Test=$dynamic_1024$8ad8757baa8564dc136c1e07507f4a98:test3
Test=$dynamic_1024$abbd6ffd1e10de751a20f97436a50dde:ThisisWorking
Test=$dynamic_1024$900150983cd24fb0d6963f7d28e17f72:abc
Test=$dynamic_1024$ffb756913c91d25a7094d3fa3d1206dd:Longer Pass Here
Test=$dynamic_1024$50a16f415160a7132a86a551fc34bdc9:Junk
Test=$dynamic_1024$1d5615d1e07f2554970cb4a93d776ca3:Junk1
Test=$dynamic_1024$778f5c751de2d5a32faf89626c786103:Junky3

####################################################################
# mkmd5gen generated DYNAMIC type for md5($p)
####################################################################
[List.Generic:dynamic_1025]
Expression=dynamic_1025: md5($p)
Flag=MGF_KEYS_INPUT
Func=DynamicFunc__crypt_md5
Test=$dynamic_1025$5a105e8b9d40e1329780d62ea2265d8a:test1
Test=$dynamic_1025$8ad8757baa8564dc136c1e07507f4a98:test3
Test=$dynamic_1025$900150983cd24fb0d6963f7d28e17f72:abc

[List.Generic:dynamic_1030]
Expression=dynamic_1030: md5($p.$p)
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1030$42b72f913c3201fc62660d512f5ac746:test1

[List.Generic:dynamic_1031]
Expression=dynamic_1031: md5($s.md5($p).$p)
Flag=MGF_SALTED
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__SSEtoX86_switch_input1
Func=DynamicFunc__ToSSE
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__SSEtoX86_switch_input1
Func=DynamicFunc__ToSSE
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_keys
Func=DynamicFunc__SSEtoX86_switch_input1
Func=DynamicFunc__ToSSE
Func=DynamicFunc__crypt_md5
Test=$dynamic_1031$a459f60614498dbdd9a79dcc9c538749$aabbccdd:test1

[List.Generic:dynamic_1032]
Expression=dynamic_1032: md5(md5($s).md5($p).$p)
Flag=MGF_SALTED
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1032$042d1f15ed57929a2ac8ee4f0a924679$aabbccdd:test1


[List.Generic:dynamic_1042]
Expression=dynamic_1042: md5(md5($s).md5($p).$p)
Flag=MGF_SALTED
Flag=MGF_SALT_AS_HEX
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1042$042d1f15ed57929a2ac8ee4f0a924679$aabbccdd:test1


[List.Generic:dynamic_1052]
Expression=dynamic_1052: md5(md5($s).md5($p).$p)
Flag=MGF_SALTED
Flag=MGF_SALT_AS_HEX
Flag=MGF_KEYS_BASE16_IN1_Offset32
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__overwrite_salt_to_input1_no_size_fix
Func=DynamicFunc__set_input_len_64
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1052$042d1f15ed57929a2ac8ee4f0a924679$aabbccdd:test1

[List.Generic:dynamic_1062]
Expression=dynamic_1062: md5(md5($s).md5($p).$p)
Flag=MGF_SALTED
Flag=MGF_SALT_AS_HEX
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__X86toSSE_switch_output1
Test=$dynamic_1062$042d1f15ed57929a2ac8ee4f0a924679$aabbccdd:test1

[List.Generic:dynamic_1033]
Expression=dynamic_1033: md5(md5($p).$s)
Flag=MGF_SALTED
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1033$e9fb44106edf60419d26a10b5439d0c7$aabbccddeeff:test1
SaltLen=12

[List.Generic:dynamic_1043]
Expression=dynamic_1043: md5(md5($p).$s)
Flag=MGF_SALTED
Flag=MGF_KEYS_BASE16_IN1
Func=DynamicFunc__set_input_len_32
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1043$e9fb44106edf60419d26a10b5439d0c7$aabbccddeeff:test1
SaltLen=12


[List.Generic:dynamic_1044]
Expression=dynamic_1044: md5(md5($p.md5($p.md5($s.$p))).$s)
Flag=MGF_SALTED
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_salt2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1044$e4f0714e3e616ed7647dbd44aaff3c00$aabbccdd:test1



####################################################################
# Simple DYNAMIC type for md5(md5($p).md5($p))
####################################################################
[List.Generic:dynamic_1063]
# expression shown will be the string:   dynamic_1063 md5(md5($p).md5($p))
Expression=md5(md5($p).md5($p))
# NOTE, this format does NOT work on SSE2.  It requires a md5() of a 64 byte string.
# SSE (or MMX) is limtited to 54 byte max password, due to 'enhancements'
# Thus, we need a non-sse2 safe flag.
#Flag=MGF_NOTSSE2Safe
Flag=MGF_KEYS_INPUT
# here is the optimized 'script' to perform hash 'like' IPB but salt replaced with password.
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
Func=DynamicFunc__X86toSSE_switch_output1
# These are test strings for this format.
Test=$dynamic_1063$478b10974f15e7295883224fd286ccba:test1
Test=$dynamic_1063$18a59101e6c6fb38260d542a394ecb22:thatsworking
Test=$dynamic_1063$630b01b68b6db6fd43a751f8147d1faf:test3


####################################################################
# mkmd5gen generated DYNAMIC type for md5($s.md5($p.$s.md5(md5($s2)).$c1.$c3.$c4.$c5.$c7.$c6.$c8).$c2)
####################################################################
[List.Generic:dynamic_5001]
Expression=dynamic_5001: md5($s.md5($p.$s.md5(md5($s2)).$c1.$c3.$c4.$c5.$c7.$c6.$c8).$c2)
Flag=MGF_SALTED
Flag=MGF_SALTED2
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_2nd_salt
Func=DynamicFunc__crypt_md5_in1_to_out2
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST1
Func=DynamicFunc__append_input2_from_CONST3
Func=DynamicFunc__append_input2_from_CONST4
Func=DynamicFunc__append_input2_from_CONST5
Func=DynamicFunc__append_input2_from_CONST7
Func=DynamicFunc__append_input2_from_CONST6
Func=DynamicFunc__append_input2_from_CONST8
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
Func=DynamicFunc__append_input1_from_CONST2
Func=DynamicFunc__crypt_md5
Test=$dynamic_5001$6d1a31e525633013d52bc065328ecd7c$aabbccdd$$2xszef:test1
Test=$dynamic_5001$1ad6b42d7f22fcafbf9609c3efbea182$zzyyxxww$$2ER#^s:test3
Test=$dynamic_5001$5eeeca352107c1b565af4a249a8452ee$01234567$$2hhhas:ThisisWorking
Test=$dynamic_5001$fdb9c4702c7f82e3431d64f3b9d06136$98765432$$2hrhas:abc
Test=$dynamic_5001$4b1fce298a29af07a8d66542222fb36e$65432198$$2ksjas:Longer Pass Here
Test=$dynamic_5001$20d8de9d5a19e44181b0a8102e9345e4$32165498$$2Zhbbb:Junk
Test=$dynamic_5001$9dfb9c3259dfc316a098d6d45fc77f54$74185296$$2Thxyz:Junk1
Test=$dynamic_5001$7ea6b7fa2c1f0e2baee1792b0234034d$96325874$$2ABCDEF:Junky3
CONST1=c1
CONST2=2
CONST3=333
CONST4=4444
CONST5=c5
CONST6=666666
CONST7=const_7
CONST8=c8
SaltLen=8

[List.Generic:dynamic_5002]
Expression=dynamic_5002: md5(md5($p))
Flag=MGF_KEYS_INPUT
Func=DynamicFunc__crypt_md5_in1_to_out2
Func=DynamicFunc__SSEtoX86_switch_output2
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
#sFunc=DynamicFunc__X86toSsE_switch_output1
Test=$dynamic_5002$418d89a45edadb8ce4da17e07f72536c:test1
Test=$dynamic_5002$ccd3c4231a072b5e13856a2059d04fad:thatsworking
Test=$dynamic_5002$9992295627e7e7162bdf77f14734acf8:test3

[List.Generic:dynamic_5003]
Expression=dynamic_5003: md5($c1.md5($c2.md5($c3.md5($c4.md5($c5.md5($c6.md5($c7.md5($c8.$p).$c1).$c2).$c3).$c4).$c5).$c6).$c7)
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST8
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST7
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST1
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST6
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST5
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST3
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST4
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST4
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST3
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST5
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST6
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST1
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__append_input2_from_CONST7
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_5003$116475e5dcb0f34933dad42ba23f1c5d:test1
Test=$dynamic_5003$78a24cda1176ee0a361204ad523e84db:test3
Test=$dynamic_5003$33dd28264da1606c681d883947e1dc69:ThisisWorking
Test=$dynamic_5003$7ee41d938e2711ec317f2b6bebd27438:abc
Test=$dynamic_5003$31a334471399b2fe1ed00badc36d3163:Longer Pass Here
Test=$dynamic_5003$1a4eda9b6cd32febe9f9aa5d0a7f53ec:Junk
Test=$dynamic_5003$c3c4959e6cde82d9035fbc55608a53ef:Junk1
Test=$dynamic_5003$75242eca2f979739a1b047f74f9b6851:Junky3
CONST1=1
CONST2=c2
CONST3=333
CONST4=4..4
CONST5=5---5
CONST6=6,,,,6
CONST7=7!!!!!7
CONST8=88888888

[List.Generic:dynamic_5004]
Expression=dynamic_5004: md5($c1.md5($c2.md5($c3.md5($c4.md5($c5.md5($c6.md5($c7.md5($c8.$p).$c1).$c2).$c3).$c4).$c5).$c6).$c7)
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST8
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST7
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST6
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST2
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST5
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST3
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST4
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST4
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST3
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST5
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST2
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST6
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST7
Func=DynamicFunc__crypt_md5
Test=$dynamic_5004$116475e5dcb0f34933dad42ba23f1c5d:test1
Test=$dynamic_5004$78a24cda1176ee0a361204ad523e84db:test3
Test=$dynamic_5004$33dd28264da1606c681d883947e1dc69:ThisisWorking
Test=$dynamic_5004$7ee41d938e2711ec317f2b6bebd27438:abc
Test=$dynamic_5004$31a334471399b2fe1ed00badc36d3163:Longer Pass Here
Test=$dynamic_5004$1a4eda9b6cd32febe9f9aa5d0a7f53ec:Junk
Test=$dynamic_5004$c3c4959e6cde82d9035fbc55608a53ef:Junk1
Test=$dynamic_5004$75242eca2f979739a1b047f74f9b6851:Junky3
CONST1=1
CONST2=c2
CONST3=333
CONST4=4..4
CONST5=5---5
CONST6=6,,,,6
CONST7=7!!!!!7
CONST8=88888888

[List.Generic:dynamic_5005]
Expression=dynamic_5005: md5($c1.md5($c2.md5($c3.md5($c4.md5($c5.md5($c6.md5($c7.md5($c8.$p).$c1).$c2).$c3).$c4).$c5).$c6).$c7)
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input1_from_CONST8
Func=DynamicFunc__append_input2_from_input
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt_md5_in2_to_out1
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST7
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__append_input2_from_CONST1
Func=DynamicFunc__crypt_md5_in2_to_out1
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_input2_from_CONST6
Func=DynamicFunc__append_input_from_input2
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST2
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST5
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST3
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST4
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST4
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST3
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST5
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST2
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST6
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST7
Func=DynamicFunc__crypt_md5
Test=$dynamic_5005$116475e5dcb0f34933dad42ba23f1c5d:test1
Test=$dynamic_5005$78a24cda1176ee0a361204ad523e84db:test3
Test=$dynamic_5005$33dd28264da1606c681d883947e1dc69:ThisisWorking
Test=$dynamic_5005$7ee41d938e2711ec317f2b6bebd27438:abc
Test=$dynamic_5005$31a334471399b2fe1ed00badc36d3163:Longer Pass Here
Test=$dynamic_5005$1a4eda9b6cd32febe9f9aa5d0a7f53ec:Junk
Test=$dynamic_5005$c3c4959e6cde82d9035fbc55608a53ef:Junk1
Test=$dynamic_5005$75242eca2f979739a1b047f74f9b6851:Junky3
CONST1=1
CONST2=c2
CONST3=333
CONST4=4..4
CONST5=5---5
CONST6=6,,,,6
CONST7=7!!!!!7
CONST8=88888888

# -num=5003 -const1=1 -const2=c2 -const3=333 -const4-4..4 -const5-5---5 -const6=6,,,,6 -const7=7!!!!!7 -const8=88888888  md5($c1.md5($c2.md5($c3.md5($c4.md5($c5.md5($c6.md5($c7.md5($c8.$p).$c1).$c2).$c3).$c4).$c5).$c6).$c7)

[List.Generic:dynamic_1053]
Expression=dynamic_1053: md5($s.md5($s).$p)
Flag=MGF_SALTED
Flag=MGF_SALT_AS_HEX_TO_SALT2
Func=DynamicFunc__clean_input
#Func=DynamicFunc__append_salt
#Func=DynamicFunc__crypt_md5
#Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
#Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_2nd_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1053$94eead61c1c07bb832ff855dd6115702$aabbccdd:test1
Test=$dynamic_1053$0c6253b0b82dc81bc5f5de0dcb59ee80$zzyyxxww:test3
Test=$dynamic_1053$86d00c5383bbdaf1661d7729160766ed$01234567:ThisisWorking
Test=$dynamic_1053$22c4c1568720e6db338d672c8241d0e4$98765432:abc
Test=$dynamic_1053$f55723bdf8e577bf691b079aeadffe1f$32165498:Junk
Test=$dynamic_1053$133857938ba2921bb079b3eca170bc77$74185296:Junk1
Test=$dynamic_1053$b24df1c9f98699a99e492a591bdf1241$96325874:Junky3


[List.Generic:dynamic_1054]
Expression=dynamic_1054: md5($s.md5(unicode($p)))
Flag=MGF_SALTED
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__setmode_normal
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
Test=$dynamic_1054$2e233d5102d2be03307ec5490075121d$aabbccdd:test1
Test=$dynamic_1054$0474efd4022743c1113636fc1f34d88a$zzyyxxww:test3
Test=$dynamic_1054$e3ab32d7db8521616781f13919bb17bc$01234567:ThisisWorking
Test=$dynamic_1054$9bc5270127c0a61c031ede0a61a79d98$98765432:abc
Test=$dynamic_1054$e3546ff029afaba5977ed1db0da8b467$32165498:Junk
Test=$dynamic_1054$e7c1a5215b06a7ee4048d70b50d086dd$74185296:Junk1
Test=$dynamic_1054$66d4e0e951d5f064538b9b2db3bdf41d$96325874:Junky3


[List.Generic:dynamic_1055]
Expression=dynamic_1055: md5($s.md5(unicode($p)))
Flag=MGF_SALTED
Flag=MGF_KEYS_UNICODE_B4_CRYPT
Flag=MGF_KEYS_CRYPT_IN2
Func=DynamicFunc__setmode_normal
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
Func=DynamicFunc__crypt_md5
Test=$dynamic_1055$2e233d5102d2be03307ec5490075121d$aabbccdd:test1
Test=$dynamic_1055$0474efd4022743c1113636fc1f34d88a$zzyyxxww:test3
Test=$dynamic_1055$e3ab32d7db8521616781f13919bb17bc$01234567:ThisisWorking
Test=$dynamic_1055$9bc5270127c0a61c031ede0a61a79d98$98765432:abc
Test=$dynamic_1055$e3546ff029afaba5977ed1db0da8b467$32165498:Junk
Test=$dynamic_1055$e7c1a5215b06a7ee4048d70b50d086dd$74185296:Junk1
Test=$dynamic_1055$66d4e0e951d5f064538b9b2db3bdf41d$96325874:Junky3


[List.Generic:dynamic_1056]
Expression=dynamic_1056: md5(unicode($p).md5(unicode($s)))
Flag=MGF_SALTED
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__setmode_normal
Func=DynamicFunc__crypt_md5
Test=$dynamic_1056$34ba9cf3d8f61e6eb21942feb1164261$aabbccdd:test1
Test=$dynamic_1056$7b4fdbaab27929e18808c96e41b846f0$zzyyxxww:test3


[List.Generic:dynamic_1057]
Expression=dynamic_1057: md5(unicode($p).md5(unicode($s)))
Flag=MGF_SALTED
Flag=MGF_SALT_UNICODE_B4_CRYPT
Flag=MGF_SALT_AS_HEX
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__setmode_normal
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1057$34ba9cf3d8f61e6eb21942feb1164261$aabbccdd:test1
Test=$dynamic_1057$7b4fdbaab27929e18808c96e41b846f0$zzyyxxww:test3

[List.Generic:dynamic_1058]
Expression=dynamic_1058: md5(unicode($p))
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1058$16c47151c18ac087cd12b3a70746c790:test1
Test=$dynamic_1058$537e738b1ac5551f65106368dc301ece:thatsworking
Test=$dynamic_1058$849ee1b88b5d887bdb058180a666b450:test3

[List.Generic:dynamic_1059]
Expression=dynamic_1059: md5(md5(unicode($p)))
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__setmode_normal
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1059$ef1b8ee3ad8690fd1678093588727529:test1
Test=$dynamic_1059$18a7e5f289730525111f7b65cb223506:thatsworking
Test=$dynamic_1059$01a0359d0cabbf49a0b0a8a941c7f5ab:test3


[List.Generic:dynamic_1090]
Expression=md5(md5($p.$s).md5(md5($p).$s.md5($s.md5($s.$p))).md5(md5($s.$p.$p).$s.md5($p.md5($p.$s.$p))).$p.md5($s.$p.md5($s.$p.$s).$p.md5($s.$p.md5($p.$s.$s))))
Flag=MGF_SALTED
Flag=MGF_NOTSSE2Safe
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt_in2_to_out1
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__crypt_md5_in2_to_out1
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__append_from_last_output2_to_input1_as_base16
Func=DynamicFunc__append_keys
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__crypt_md5_in2_to_out1
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__crypt_in2_to_out1
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
Test=$dynamic_1090$e49995dd9328ba663e8839734aaef068$zRwIEOWX:hallo


[List.Generic:dynamic_1061]
Expression=dynamic_1061: md5($s.'-'.md5($p))
Flag=MGF_SALTED
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__crypt_md5
Test=$dynamic_1061$7fc1aa71056429e9f773b2cde1106aee$salt1:test1
Test=$dynamic_1061$abdb2b7fbc78abaf475d3a5bb48b48bc$2tlas:testword2
CONST1=-

# md5(sha1($p))
[List.Generic:dynamic_1062]
Expression=dynamic_1062: md5(sha1($p))
Flag=MGF_StartInX86Mode
Flag=MGF_KEYS_INPUT_BE_SAFE
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__SHA1_crypt_md5_input1_append_input2_base16
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1062$a7168f0f249e3add33da11a59e228a57:test1
Test=$dynamic_1062$067dda3ad565339fffa61ba74fab0ba3:thatsworking
Test=$dynamic_1062$71a1083be5c288da7e57b8c2bd7cbc96:test3

# sha1(md5($p))
[List.Generic:dynamic_1063]
Expression=dynamic_1063: sha1(md5($p))
Flag=MGF_KEYS_INPUT
Flag=MGF_INPUT_20_BYTE
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__SHA1_crypt_md5_input2_to_output1_FINAL
Test=$dynamic_1063$81d84525eb1499d518cf3cb3efcbe1d11c4ccf25:test1
Test=$dynamic_1063$6cd62e1767b65eec58d687de6d9c08a828018254:thatsworking
Test=$dynamic_1063$7d653cf00d747a9fbab213b6c2b335cfe8199ff3:test3


# sha1($p)
[List.Generic:dynamic_1064]
Expression=dynamic_1064: sha1($p)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_FLAT_BUFFERS
Func=DynamicFunc__SHA1_crypt_md5_input1_to_output1_FINAL
Test=$dynamic_1064$b444ac06613fc8d63795be9ad0beaf55011936ac:test1
Test=$dynamic_1064$1068db2941b46d12f790df99d72fe8c2eb6d3aaf:thatsworking
Test=$dynamic_1064$3ebfa301dc59196f18593c45e519287a23297589:test3

[List.Generic:dynamic_1165]
Expression=dynamic_1165: md5(sha1(md5(sha1($p))))
Flag=MGF_StartInX86Mode
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_md5_input1_append_input2_base16
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__clean_input2
Func=DynamicFunc__SHA1_crypt_md5_input1_append_input2_base16
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1165$62f2dd3da9a39eea4314b846aaa02a3d:test1
Test=$dynamic_1165$288f19a5a0967ef40d8e9ebfef79b0ea:thatsworking
Test=$dynamic_1165$72a4064912ccaea4b982b3e9fc710cd7:test3

[List.Generic:dynamic_1066]
Expression=dynamic_1066: sha1($p.$s)
Flag=MGF_NOTSSE2Safe
Flag=MGF_SALTED
Flag=MGF_INPUT_20_BYTE
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__SHA1_crypt_md5_input1_to_output1_FINAL
Test=$dynamic_1066$5a12479f0a8286a832288e1dc2ea9b2eda4e382d$sG:test1
Test=$dynamic_1066$c72b6f1caddb158831cab0b08d29243ea20fc869$xxRW:thatsworking
Test=$dynamic_1066$b966eff1aac95e92818a7c59326cce297b935eff$s3xx:test3

[List.Generic:dynamic_1067]
Expression=dynamic_1067: sha1($s.$p)
Flag=MGF_NOTSSE2Safe
Flag=MGF_SALTED
Flag=MGF_INPUT_20_BYTE
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_md5_input1_to_output1_FINAL
Test=$dynamic_1067$f5266f29ff7f1ea6fc30085c8347fcf6a6e36e9c$sG:test1
Test=$dynamic_1067$a34af873d9047541b4d76ceae7b391f0664ca99e$xxRW:thatsworking
Test=$dynamic_1067$f0058038be0e821caa3031b463aed00fbe7e3beb$s3xx:test3

[List.Generic:dynamic_1068]
Expression=dynamic_1068: sha1(sha1($p))
Flag=MGF_NOTSSE2Safe
Flag=MGF_INPUT_20_BYTE
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_md5_input1_append_input2_base16
Func=DynamicFunc__SHA1_crypt_md5_input2_to_output1_FINAL
Test=$dynamic_1068$2a367cbb171d78d293f40fd7d1defb31e3fb1728:test1
Test=$dynamic_1068$d3e0a4d2f3ec196a79ab2890c65144c20ff24ed8:thatsworking
Test=$dynamic_1068$b13773cfee62f832cacb618b257feec972f30b13:test3

[List.Generic:dynamic_1069]
Expression=dynamic_1069: sha1(sha1($p))
Flag=MGF_NOTSSE2Safe
Flag=MGF_INPUT_20_BYTE
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_md5_input1_append_input2_base16
Func=DynamicFunc__SHA1_crypt_md5_input2_overwrite_input2_base16
Func=DynamicFunc__SHA1_crypt_md5_input2_to_output1_FINAL
Test=$dynamic_1069$00aeb6dc5e6269a6b2f39728cd8a6812a644dae6:test1
Test=$dynamic_1069$d60112917380bb0641f5d1a4ce8fb77e958452f7:thatsworking
Test=$dynamic_1069$54e45916fb79f7be1c695828fdba44915b50751e:test3

[List.Generic:dynamic_1065]
Expression=dynamic_1065: md5(sha1(md5(sha1($p))))
Flag=MGF_StartInX86Mode
Flag=MGF_KEYS_INPUT_BE_SAFE
Func=DynamicFunc__clean_input2
Func=DynamicFunc__SHA1_crypt_input1_append_input2_base16
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2_base16
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1065$62f2dd3da9a39eea4314b846aaa02a3d:test1
Test=$dynamic_1065$288f19a5a0967ef40d8e9ebfef79b0ea:thatsworking
Test=$dynamic_1065$72a4064912ccaea4b982b3e9fc710cd7:test3

[List.Generic:dynamic_1070]
Expression=dynamic_1070: sha1(upr($u).':'.$p)
Flag=MGF_NOTSSE2Safe
Flag=MGF_INPUT_20_BYTE
Flag=MGF_USERNAME_UPCASE
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1070$20fa5cf803e363ba9f13e12ca61586e2efabc737$$UROOT:test1
Test=$dynamic_1070$2e839b0c32af0e2099584ea22f20d2f2c477d908$$UJAS0N:thatsworking
Test=$dynamic_1070$55496ed9edcabdd2368e7cd22eaae633c6d6c40e$$UJOEBLOW:test3
CONST1=:

# this format is unicode raw-md5.  It shows the TestA= and TestU=. These will only
# be loaded depending upon --encoding=utf-8.  If that is not present, then the TestA=
# lines are loaded.  If --encoding=utf-8 IS set, then the TestU= lines are loaded.
# the Test= lines are always loaded.
[List.Generic:dynamic_1100]
Expression=md5(unicode($p))  [rawmd5-unicode utf-8 input)]
Flag=MGF_UTF8
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
TestU=$dynamic_1100$94a4e171de16580742c4d141e6607bf7:\xE2\x82\xAC
TestU=$dynamic_1400$03c60810f0e54d16e826aca385d776c8:\xE2\x82\xAC\xE2\x82\xAC
TestU=$dynamic_1100$2d554433d7cde7ec8d16aaf126c3be6b:\xE2\x82\xAC\xC3\xBC
TestU=$dynamic_1100$8007d9070b27db7b30433df2cd10abc1:\xC3\xBC\xE2\x82\xAC
TestA=$dynamic_1100$ea7ab2b5c07650badab30790d0c9b63e:\xFC
TestA=$dynamic_1100$f0a0b9f1dea0e458cec9a284ff434d44:\xFC\xFC
TestA=$dynamic_1100$d25a0b436b768777cc9a343d283dbf5a:\xFC\xFC\xFC
TestA=$dynamic_1100$719917322bf12168f8c55939e4fec8de:\xFC\xFC\xFC\xFC
Test=$dynamic_1100$16c47151c18ac087cd12b3a70746c790:test1
Test=$dynamic_1100$d41d8cd98f00b204e9800998ecf8427e:
Test=$dynamic_1100$9c3abef89ff76f8acd80eae37b35f64f:test2
Test=$dynamic_1100$849ee1b88b5d887bdb058180a666b450:test3
Test=$dynamic_1100$8c4cb7e8b33b56a833cdaa8673f3b425:test4
Test=$dynamic_1100$537e738b1ac5551f65106368dc301ece:thatsworking

####################################################################
# sha1(UTF16LE($p))
####################################################################
[List.Generic:dynamic_1101]
Expression=sha1(unicode($p))
Flag=MGF_INPUT_20_BYTE
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1101$87f8ed9157125ffc4da9e06a7b8011ad80a53fe1:test
Test=$dynamic_1101$28f69840b617ece11b24fcb4bd380f6aa4569d60:thatsworking
Test=$dynamic_1101$ab7a9c43394b8631a2947e064efeb19a48c5ed89:test3

[List.Generic:dynamic_1400]
Expression=md5($s.:asterisk:.$p) [Asterisk SIP]
Flag=MGF_SALTED
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
CONST1=:asterisk:
Test=$dynamic_1400$4a8e71480c5b1ef0a5d502a8eb98576a$1234:abcd

[List.Generic:dynamic_1401]
Expression=md5($u.:asterisk:.$p) [Asterisk SIP]
Flag=MGF_USERNAME
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
CONST1=:asterisk:
Test=$dynamic_1401$4a8e71480c5b1ef0a5d502a8eb98576a:abcd:1234

[List.Generic:dynamic_1403]
Expression=dynamic_1403: sha1(upr($u).':'.$p)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_USERNAME_UPCASE
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1403$20fa5cf803e363ba9f13e12ca61586e2efabc737$$UROOT:test1
Test=$dynamic_1403$2e839b0c32af0e2099584ea22f20d2f2c477d908$$UJAS0N:thatsworking
Test=$dynamic_1403$55496ed9edcabdd2368e7cd22eaae633c6d6c40e$$UJOEBLOW:test3
Test=$dynamic_1403$20fa5cf803e363ba9f13e12ca61586e2efabc737:test1:root
CONST1=:

[List.Generic:dynamic_1404]
Expression=dynamic_1404: md5($s.':'.upr($p))
Flag=MGF_SALTED
Flag=MGF_PASSWORD_UPCASE
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1404$d097de40a351866a024e6ba9ce2595eb$f8sKST:TEST1
Test=$dynamic_1404$496ff0193acd6b92f92f9a8e05d3f2f2$tVJ9Gu:TESTLONG
Test=$dynamic_1404$67557c91c31c1803a9fd55454ca102fe$IIpTKQ:HAPPY
Test=$dynamic_1404$d097de40a351866a024e6ba9ce2595eb$f8sKST:TEST1
CONST1=:

####################################################################
####################################################################
# END of DYNAMIC examples
####################################################################
####################################################################
