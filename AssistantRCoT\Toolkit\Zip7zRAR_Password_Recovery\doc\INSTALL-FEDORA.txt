Please, read INSTAL<PERSON> for general information about installing <PERSON> on your
system.

== Preamble

    Important: it's actually about a local to a user's home directory, not a
    system-wide installation. In fact, most likely you do not need to install
    <PERSON> the Ripper system-wide.  Instead, after you compile the source code
    (see below), you may simply enter the "run" directory and invoke <PERSON>
    from there, e.g., ./john --list=build-options.

    Commands not explicitly prefixed by sudo must be run as a regular user
    (the one that will use JtR) rather than as root.

== How to install on Fedora

    If anything below fails, run 'sudo dnf update' and re-try.

=== Preconditions and Required stuff

    sudo dnf update
    sudo dnf install git gcc openssl-devel

==== Recommended (extra formats and performance)

    sudo dnf install yasm gmp-devel libpcap-devel bzip2-devel

==== Optional MPI support

    You probably do not need MPI, it's only for multi-machine clusters with
    shared network storage.  Merely having it compiled in (even if unused)
    may have security and reliability drawbacks.  Most users should read up
    on the --fork options instead, which gets compiled in automagically if
    your system supports it.  Having said all this, if you're sure you do
    need MPI support: Read README.mpi for instructions.

    Please also note that OpenMP (Open Multi-Processing) is not the same
    as Open MPI (Message Passing Interface).  You do get OpenMP if your
    system supports it.

=== Clone latest bleeding-edge Jumbo and build

==== Clone GIT repo

    mkdir -p ~/src
    cd ~/src
    git clone git://github.com/magnumripper/JohnTheRipper -b bleeding-jumbo john

==== Build

    cd ~/src/john/src
    ./configure && make -s clean && make -sj4

=== Install TAB-completion

    sudo make shell-completion

=== Test your build

    $ ../run/john --test=0

=== Benchmark your build

    $ ../run/john --test
