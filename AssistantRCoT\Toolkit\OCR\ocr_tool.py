"""
智能OCR文字识别工具 - 自动调用不同API接口，支持单文件、URL与批量处理
"""
import json
import re
from pathlib import Path
from typing import Union, Dict, Any, List, Tuple
import logging
from enum import Enum
import os
import sys
import tempfile
from io import BytesIO
import fitz  # PyMuPDF
from PIL import Image
import io
import csv
import traceback

from alibabacloud_ocr_api20210707.client import Client as ocr_api20210707Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_ocr_api20210707 import models as ocr_api20210707_models
from alibabacloud_tea_util import models as util_models

# 添加openpyxl库用于处理Excel文件
try:
    import openpyxl
    EXCEL_SUPPORT = True
except ImportError:
    EXCEL_SUPPORT = False
    print("警告: openpyxl库未安装，无法处理Excel文件。请使用pip install openpyxl安装。")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("OCR")

class DocType(Enum):
    """文档类型枚举"""
    GENERAL = "通用文字"
    INVOICE = "票据凭证"
    ID_CARD = "个人证照"
    BUSINESS_LICENSE = "企业资质"
    EDUCATION = "教育场景"
    UNKNOWN = "未知类型"

class OCR_API_TYPE(Enum):
    """识别API类型枚举"""
    BASIC = "基础版"         # RecognizeGeneral - 通用文字识别，适用于简单文本场景
    ADVANCED = "高精版"       # RecognizeAdvanced - 全文识别高精版，适用于复杂文档、表格等
    HANDWRITING = "手写体"    # RecognizeHandwriting - 手写体识别
    UNIFIED = "统一识别"      # RecognizeAllText - OCR统一识别，根据场景自动选择
    TABLE = "表格识别"        # RecognizeTableOcr - 专门用于表格识别

def resize_and_optimize_image(image_content, max_size=5*1024*1024, format=None, quality_start=95):
    """
    调整图像大小和质量，确保小于最大尺寸
    :param image_content: 图像字节或PIL图像对象
    :param max_size: 最大大小（字节）
    :param format: 输出格式，默认保持原格式
    :param quality_start: 初始质量值
    :return: 调整后的图像字节
    """
    # 如果已经小于最大尺寸且传入的是字节，直接返回
    if isinstance(image_content, bytes) and len(image_content) <= max_size:
        return image_content
    
    try:
        # 如果传入的是字节，转换为PIL图像
        if isinstance(image_content, bytes):
            img = Image.open(BytesIO(image_content))
        else:
            img = image_content
            
        output_format = format or (img.format if hasattr(img, 'format') and img.format else 'JPEG')
        quality = quality_start
        
        while True:
            img_byte_arr = BytesIO()
            img.save(img_byte_arr, format=output_format, quality=quality)
            size = img_byte_arr.tell()
            
            if size <= max_size or quality <= 30:
                img_byte_arr.seek(0)
                return img_byte_arr.getvalue()
                
            # 降低质量
            quality -= 10
            
            # 如果质量调整到最低仍然太大，则调整图像尺寸
            if quality <= 30:
                width, height = img.size
                img = img.resize((int(width * 0.8), int(height * 0.8)), Image.LANCZOS)
                quality = 90
    
    except Exception as e:
        logger.error(f"调整图像大小失败: {str(e)}")
        # 如果传入的是字节，出错时返回原始字节
        if isinstance(image_content, bytes):
            return image_content
        # 否则返回None
        return None

def pdf_to_images(pdf_bytes, dpi=300, max_size=5*1024*1024):
    """
    将PDF转换为图像字节列表
    :param pdf_bytes: PDF文件字节内容
    :param dpi: 图像DPI
    :param max_size: 最大图像大小（字节）
    :return: 图像字节列表
    """
    # 使用PyMuPDF打开PDF
    try:
        doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        images = []
        
        for page_num in range(len(doc)):
            # 获取页面并渲染为图像
            page = doc.load_page(page_num)
            pix = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72))
            
            # 转换为PIL图像
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            
            # 调整大小和质量
            img_bytes = resize_and_optimize_image(img, max_size=max_size, format='JPEG')
            if img_bytes:
                images.append(img_bytes)
            
        return images
    except Exception as e:
        logger.error(f"PDF转换图像失败: {str(e)}")
        return []

def process_excel(file_path, output_dir):
    """
    处理Excel文件，直接读取数据并转换为CSV格式
    :param file_path: Excel文件路径
    :param output_dir: 输出目录
    :return: 处理结果字典
    """
    try:
        if not EXCEL_SUPPORT:
            return {
                "status": "error",
                "message": f"处理Excel文件 {file_path} 失败: openpyxl库未安装，请使用pip install openpyxl安装"
            }
        
        # 加载Excel工作簿
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        file_path = Path(file_path)
        
        all_csv_paths = []
        
        # 处理每个工作表
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            
            # 为每个工作表创建单独的CSV文件
            output_name = f"{file_path.stem}_{sheet_name}" if len(workbook.sheetnames) > 1 else f"{file_path.stem}"
            csv_output_path = Path(output_dir) / f"{output_name}.csv"
            all_csv_paths.append(str(csv_output_path))
            
            # 使用CSV模块写入数据
            with open(csv_output_path, 'w', newline='', encoding='utf-8') as f:
                csv_writer = csv.writer(f)
                
                # 写入所有行数据
                for row in sheet.iter_rows(values_only=True):
                    # 将None值转换为空字符串
                    row_data = ['' if cell is None else str(cell) for cell in row]
                    csv_writer.writerow(row_data)
        
        return {
            "status": "success",
            "message": f"处理成功: {file_path}",
            "doc_type": "表格数据",
            "csv_results": all_csv_paths
        }
        
    except Exception as e:
        logging.error(f"处理Excel文件 {file_path} 失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "message": f"处理Excel文件 {file_path} 失败: {str(e)}"
        }

class OCRTool:
    """OCR文字识别工具类"""
    
    def __init__(self):
        """初始化OCR工具"""
        # 初始化配置
        self.config = self._load_config()
        # 初始化API客户端
        self.client = self._init_client()
        # 初始化统计
        self.stats = self._init_or_load_stats()
        
        # 初始化文件名匹配模式和对应的文档类型
        self.filename_patterns = {
            # 优先匹配会议纪要和规划许可证等特殊文件类型
            r'会议纪要|府纪要|纪要': DocType.GENERAL,
            r'建设工程规划许可证|规划许可': DocType.GENERAL,
            
            # 其他匹配模式
            r'发票|收据|凭证|票据': DocType.INVOICE,
            r'身份证|驾驶证|护照|社保卡': DocType.ID_CARD,
            r'营业执照|许可证|资质证书': DocType.BUSINESS_LICENSE,
            r'毕业证|学历|成绩单|学位证': DocType.EDUCATION,
            
            # 默认类型
            r'.*': DocType.GENERAL
        }
        
        # 增加文件名关键词与API类型的映射
        self.api_type_patterns = {
            r'手写|笔记|手稿': OCR_API_TYPE.HANDWRITING,
            r'表格|清单|列表|报表': OCR_API_TYPE.TABLE,
            r'复杂|高精度|书籍|文章|会议纪要|府纪要|纪要': OCR_API_TYPE.ADVANCED,
            r'简单|便签|短文本': OCR_API_TYPE.BASIC
        }
        
    def _load_config(self):
        """加载OCR工具配置"""
        from configparser import ConfigParser
        
        # 确定配置文件路径
        config_path = Path(__file__).parent / "OCR.conf"
        stats_path = Path(__file__).parent / "ocr_stats.json"
        
        # 初始化默认配置
        config = {
            "max_image_size": 5 * 1024 * 1024,  # 5MB
            "pdf_dpi": 300,
            "retry_count": 3,
            "stats_path": str(stats_path),
            "api_keys": {
                "key_id": "",
                "key_secret": ""
            },
            "endpoint": "ocr-api.cn-hangzhou.aliyuncs.com"
        }
        
        # 如果配置文件存在，读取其中的配置
        if config_path.exists():
            parser = ConfigParser()
            parser.read(config_path)
            
            if "ALIYUN_OCR" in parser:
                if "key_id" in parser["ALIYUN_OCR"]:
                    config["api_keys"]["key_id"] = parser["ALIYUN_OCR"]["key_id"]
                if "key_secret" in parser["ALIYUN_OCR"]:
                    config["api_keys"]["key_secret"] = parser["ALIYUN_OCR"]["key_secret"]
                if "endpoint" in parser["ALIYUN_OCR"]:
                    config["endpoint"] = parser["ALIYUN_OCR"]["endpoint"]
            
            if "OCR_SETTINGS" in parser:
                if "max_image_size" in parser["OCR_SETTINGS"]:
                    config["max_image_size"] = int(parser["OCR_SETTINGS"]["max_image_size"])
                if "pdf_dpi" in parser["OCR_SETTINGS"]:
                    config["pdf_dpi"] = int(parser["OCR_SETTINGS"]["pdf_dpi"])
                if "retry_count" in parser["OCR_SETTINGS"]:
                    config["retry_count"] = int(parser["OCR_SETTINGS"]["retry_count"])
        else:
            logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
        
        # 检查API密钥是否存在
        if not config["api_keys"]["key_id"] or not config["api_keys"]["key_secret"]:
            logger.error("API密钥未配置，请在配置文件中添加key_id和key_secret")
            raise ValueError("API密钥未配置，请在配置文件中添加key_id和key_secret")
        
        return config
        
    def _init_client(self):
        """初始化阿里云OCR客户端"""
        # 创建配置对象
        config_obj = open_api_models.Config(
            access_key_id=self.config["api_keys"]["key_id"],
            access_key_secret=self.config["api_keys"]["key_secret"],
            endpoint=self.config["endpoint"]
        )
        
        # 返回客户端
        return ocr_api20210707Client(config_obj)
    
    def _init_or_load_stats(self):
        """初始化或加载使用统计"""
        stats_path = Path(self.config["stats_path"])
        
        # 如果统计文件存在，加载它
        if stats_path.exists():
            try:
                with open(stats_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载统计文件失败: {e}")
        
        # 初始化新的统计对象
        return {
            "doc_type_counts": {
                DocType.GENERAL.value: 0,
                DocType.INVOICE.value: 0, 
                DocType.ID_CARD.value: 0,
                DocType.BUSINESS_LICENSE.value: 0,
                DocType.EDUCATION.value: 0,
                DocType.UNKNOWN.value: 0,
            },
            "api_type_counts": {
                OCR_API_TYPE.BASIC.value: 0,
                OCR_API_TYPE.ADVANCED.value: 0,
                OCR_API_TYPE.HANDWRITING.value: 0,
                OCR_API_TYPE.UNIFIED.value: 0,
                OCR_API_TYPE.TABLE.value: 0
            },
            "errors": {},
            "total_calls": 0,
            "last_update": ""
        }

    def _call_api(self, doc_type, api_type, content=None, url=None, runtime_options=None):
        """
        统一的API调用函数，支持内容或URL
        :param doc_type: 文档类型
        :param api_type: API类型
        :param content: 二进制内容
        :param url: 图片URL
        :param runtime_options: 运行时选项
        :return: API响应
        """
        if runtime_options is None:
            runtime_options = util_models.RuntimeOptions(
                read_timeout=30000,
                connect_timeout=30000
            )
        
        # 对特定文档类型使用专用API
        if doc_type == DocType.INVOICE:
            # 票据识别
            request = ocr_api20210707_models.RecognizeMixedInvoicesRequest()
            if url:
                request.url = url
            else:
                request.body = content
            logger.info("调用混贴发票识别API")
            return self.client.recognize_mixed_invoices_with_options(request, runtime_options)
            
        elif doc_type == DocType.ID_CARD:
            # 身份证识别
            request = ocr_api20210707_models.RecognizeIdcardRequest()
            if url:
                request.url = url
            else:
                request.body = content
            request.side = "face"  # 默认正面
            logger.info("调用身份证识别API")
            return self.client.recognize_idcard_with_options(request, runtime_options)
            
        elif doc_type == DocType.BUSINESS_LICENSE:
            # 营业执照识别
            request = ocr_api20210707_models.RecognizeBusinessLicenseRequest()
            if url:
                request.url = url
            else:
                request.body = content
            logger.info("调用营业执照识别API")
            return self.client.recognize_business_license_with_options(request, runtime_options)
            
        elif doc_type == DocType.EDUCATION:
            # 教育试卷识别
            request = ocr_api20210707_models.RecognizeEduPaperOcrRequest()
            if url:
                request.url = url
            else:
                request.body = content
            logger.info("调用试卷识别API")
            return self.client.recognize_edu_paper_ocr_with_options(request, runtime_options)
        
        # 通用文字类型 (DocType.GENERAL) 根据API类型选择合适的通用文字识别API
        if api_type == OCR_API_TYPE.TABLE:
            # 表格识别
            request = ocr_api20210707_models.RecognizeTableOcrRequest()
            if url:
                request.url = url
            else:
                request.body = content
            logger.info("调用表格识别API")
            return self.client.recognize_table_ocr_with_options(request, runtime_options)
            
        elif api_type == OCR_API_TYPE.HANDWRITING:
            # 手写体识别
            request = ocr_api20210707_models.RecognizeHandwritingRequest()
            if url:
                request.url = url
            else:
                request.body = content
            logger.info("调用手写体识别API")
            return self.client.recognize_handwriting_with_options(request, runtime_options)
            
        elif api_type == OCR_API_TYPE.ADVANCED:
            # 通用文字识别高精版
            request = ocr_api20210707_models.RecognizeAdvancedRequest()
            if url:
                request.url = url
            else:
                request.body = content
            request.output_char_info = True
            request.output_table = True
            request.output_paragraph = True
            logger.info("调用通用文字识别高精版API")
            return self.client.recognize_advanced_with_options(request, runtime_options)
            
        elif api_type == OCR_API_TYPE.UNIFIED:
            # OCR统一识别
            request = ocr_api20210707_models.RecognizeAllTextRequest()
            if url:
                request.url = url
            else:
                request.body = content
            logger.info("调用OCR统一识别API")
            return self.client.recognize_all_text_with_options(request, runtime_options)
            
        else:
            # 默认使用通用文字识别(基础版)，减少资源消耗
            request = ocr_api20210707_models.RecognizeGeneralRequest()
            if url:
                request.url = url
            else:
                request.body = content
            logger.info("调用通用文字识别(基础版)API")
            return self.client.recognize_general_with_options(request, runtime_options)
    
    def _update_stats(self, doc_type, api_type, error=None):
        """
        统一更新使用统计
        :param doc_type: 文档类型
        :param api_type: API类型
        :param error: 错误信息（如果有）
        """
        # 更新文档类型和API类型计数
        self.stats["doc_type_counts"][doc_type.value] += 1
        self.stats["api_type_counts"][api_type.value] += 1
        
        # 如果有错误，更新错误统计
        if error:
            error_type = str(type(error).__name__)
            self.stats["errors"][error_type] = self.stats["errors"].get(error_type, 0) + 1
        
        # 保存统计到配置文件
        with open(self.config["stats_path"], 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
            
    def _save_results(self, response, output_dir, file_name, doc_type, api_type, is_pdf=False, is_excel=False):
        """
        统一保存处理结果
        :param response: API响应
        :param output_dir: 输出目录
        :param file_name: 文件名（不含扩展名）
        :param doc_type: 文档类型
        :param api_type: API类型
        :param is_pdf: 是否PDF文件
        :param is_excel: 是否Excel文件
        :return: 结果字典
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True, parents=True)
        
        # JSON输出总是生成
        json_output_path = output_dir / f"{file_name}_{doc_type.value}_{api_type.value}_result.json"
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(response.body.to_map(), f, ensure_ascii=False, indent=2)
        
        result = {
            "status": "success",
            "message": f"处理成功: {file_name}",
            "doc_type": doc_type.value,
            "api_type": api_type.value,
            "json_result": str(json_output_path),
            "output_format": "JSON"
        }
        
        logger.info(f"已保存JSON结果: {json_output_path}")
            
        return result

    def process_file(self, file_path: Union[str, Path], output_dir: str = None, force_type: str = None, api_type: str = None, basic_pdf: bool = False, force_general: bool = False):
        """
        处理单个文件(PDF/图片)，自动识别文档类型
        :param file_path: 文件路径
        :param output_dir: 结果输出目录，默认在与输入相同目录创建result文件夹
        :param force_type: 强制指定文档类型，可选值：通用文字/票据凭证/个人证照/企业资质/教育场景
        :param api_type: 强制指定API类型，可选值：基础版/高精版/手写体/统一识别/表格识别
        :param basic_pdf: 是否使用基本PDF处理（不转换为图像）
        :param force_general: 是否强制使用通用文字识别
        """
        file_path = Path(file_path)
        
        # 如果没有指定输出目录，则在输入文件所在目录下创建results文件夹
        if output_dir is None:
            output_dir = file_path.parent / "ocr_results"
            
        Path(output_dir).mkdir(exist_ok=True, parents=True)
        
        file_ext = file_path.suffix.lower()
        file_name = file_path.stem
        
        # 记录处理开始时间
        import datetime
        start_time = datetime.datetime.now()
        self.stats["last_update"] = start_time.strftime("%Y-%m-%d %H:%M:%S")
        self.stats["total_calls"] += 1
        
        try:
            # 检查是否是Excel文件，如果是则使用专门的处理方法，只生成CSV
            if file_ext in ['.xlsx', '.xls']:
                return process_excel(file_path, output_dir)
            
            # 读取文件二进制内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 确定文档类型和API类型
            doc_type, ocr_api_type = self._determine_processing_types(file_name, force_type, api_type, force_general)
            
            # 更新使用统计
            self._update_stats(doc_type, ocr_api_type)
            
            # 处理PDF文件
            if file_ext == '.pdf':
                return self._process_pdf_file(file_content, file_name, doc_type, ocr_api_type, output_dir, basic_pdf)
            
            # 处理图像文件
            # 调整图像大小以确保在API限制范围内
            image_content = resize_and_optimize_image(file_content, max_size=self.config["max_image_size"])
            response = self._call_api(doc_type, ocr_api_type, content=image_content)
            
            # 保存结果
            result = self._save_results(response, output_dir, file_name, doc_type, ocr_api_type)
            return result
            
        except Exception as e:
            logger.error(f"处理失败 {file_path}: {str(e)}", exc_info=True)
            # 记录错误统计
            error_type = str(type(e).__name__)
            if 'doc_type' in locals() and 'ocr_api_type' in locals():
                self._update_stats(doc_type, ocr_api_type, error=e)
            
            return {
                "status": "error",
                "message": f"处理失败 {file_path}: {str(e)}",
                "doc_type": getattr(doc_type, 'value', '未确定') if 'doc_type' in locals() else '未确定',
                "api_type": getattr(ocr_api_type, 'value', '未确定') if 'ocr_api_type' in locals() else '未确定'
            }
    
    def _determine_processing_types(self, file_name, force_type=None, api_type=None, force_general=False):
        """
        确定文档类型和API类型
        :param file_name: 文件名
        :param force_type: 强制指定的文档类型
        :param api_type: 强制指定的API类型
        :param force_general: 是否强制使用通用文字识别
        :return: 文档类型, API类型
        """
        # 确定文档类型
        if force_general:
            # 如果强制使用通用文字识别，直接设置类型为GENERAL
            doc_type = DocType.GENERAL
            logger.info(f"强制使用通用文字识别: {file_name}")
        elif force_type and force_type in [t.value for t in DocType]:
            doc_type = next(t for t in DocType if t.value == force_type)
            logger.info(f"使用指定文档类型: {doc_type.value} - {file_name}")
        else:
            doc_type = self._determine_doc_type(file_name)
            logger.info(f"自动识别文档类型: {doc_type.value} - {file_name}")
        
        # 确定使用哪种API
        if api_type:
            if isinstance(api_type, OCR_API_TYPE):
                ocr_api_type = api_type
            elif api_type in [t.value for t in OCR_API_TYPE]:
                ocr_api_type = next(t for t in OCR_API_TYPE if t.value == api_type)
            else:
                ocr_api_type = self._determine_api_type(file_name, doc_type)
            logger.info(f"使用指定API类型: {ocr_api_type.value}")
        else:
            ocr_api_type = self._determine_api_type(file_name, doc_type)
            logger.info(f"自动选择API类型: {ocr_api_type.value}")
            
        return doc_type, ocr_api_type
    
    def _process_pdf_file(self, file_content, file_name, doc_type, ocr_api_type, output_dir, basic_pdf=False):
        """
        处理PDF文件
        :param file_content: 文件内容
        :param file_name: 文件名
        :param doc_type: 文档类型
        :param ocr_api_type: API类型
        :param output_dir: 输出目录
        :param basic_pdf: 是否使用基本PDF处理
        :return: 处理结果
        """
        # 如果不使用基本PDF处理，则转换为图像
        if not basic_pdf:
            # 将PDF转换为图像
            images = pdf_to_images(file_content, dpi=self.config["pdf_dpi"], max_size=self.config["max_image_size"])
            if not images:
                return {
                    "status": "error",
                    "message": f"处理失败: 无法将PDF转换为图像",
                    "doc_type": doc_type.value,
                    "api_type": ocr_api_type.value
                }
            
            # 处理第一页图像
            image_content = images[0]
            response = self._call_api(doc_type, ocr_api_type, content=image_content)
            
            # 保存结果 - PDF只生成JSON
            result = self._save_results(response, output_dir, file_name, doc_type, ocr_api_type, is_pdf=True)
            
            # 如果PDF有多页，为每页创建单独的结果
            multi_page_results = []
            if len(images) > 1:
                for i, img in enumerate(images[1:], 1):
                    try:
                        page_response = self._call_api(doc_type, ocr_api_type, content=img)
                        # 更新统计
                        self._update_stats(doc_type, ocr_api_type)
                        
                        page_output_path = Path(output_dir) / f"{file_name}_page{i+1}_{doc_type.value}_{ocr_api_type.value}_result.json"
                        with open(page_output_path, 'w', encoding='utf-8') as f:
                            json.dump(page_response.body.to_map(), f, ensure_ascii=False, indent=2)
                        
                        multi_page_results.append(str(page_output_path))
                    except Exception as page_e:
                        logger.warning(f"处理PDF第{i+1}页失败: {str(page_e)}")
                        # 记录错误统计
                        self._update_stats(doc_type, ocr_api_type, error=page_e)
            
            result["multi_page_results"] = multi_page_results if multi_page_results else None
            return result
        
        # 使用基本PDF处理
        response = self._call_api(doc_type, ocr_api_type, content=file_content)
        
        # PDF文件只保存JSON结果
        result = self._save_results(response, output_dir, file_name, doc_type, ocr_api_type, is_pdf=True)
        
        return result

    def _determine_doc_type(self, file_name: str) -> DocType:
        """根据文件名判断可能的文档类型"""
        file_name_lower = file_name.lower()
        
        for pattern, doc_type in self.filename_patterns.items():
            if re.search(pattern, file_name_lower):
                return doc_type
        
        # 如果没有匹配上任何模式，则返回通用文字类型
        return DocType.GENERAL
        
    def _determine_api_type(self, file_name: str, doc_type: DocType) -> OCR_API_TYPE:
        """根据文件名和文档类型判断应该使用的API类型"""
        file_name_lower = file_name.lower()
        
        # 优先根据文件名关键词判断
        # 会议纪要或府纪要应该使用高精版API以便获得更好的结构化识别
        if re.search(r'会议纪要|府纪要|纪要', file_name_lower):
            return OCR_API_TYPE.ADVANCED
            
        for pattern, api_type in self.api_type_patterns.items():
            if re.search(pattern, file_name_lower):
                return api_type
                
        # 根据文档类型选择适当的API
        if doc_type == DocType.INVOICE:
            return OCR_API_TYPE.UNIFIED  # 票据用统一识别
        elif doc_type == DocType.ID_CARD:
            return OCR_API_TYPE.UNIFIED  # 证件用统一识别
        elif doc_type == DocType.BUSINESS_LICENSE:
            return OCR_API_TYPE.UNIFIED  # 营业执照用统一识别
        elif doc_type == DocType.EDUCATION:
            return OCR_API_TYPE.ADVANCED  # 教育场景用高精版
        
        # 默认使用基础版通用识别以节省配额
        return OCR_API_TYPE.BASIC

    def process_url(self, image_url: str, output_dir: str = None, force_type: str = None, api_type: str = None):
        """
        处理网络图片URL
        :param image_url: 图片URL
        :param output_dir: 结果输出目录
        :param force_type: 强制指定文档类型
        :param api_type: 强制指定API类型
        """
        # 如果没有指定输出目录，则使用默认目录
        if output_dir is None:
            output_dir = Path.cwd() / "ocr_results"
            
        Path(output_dir).mkdir(exist_ok=True, parents=True)
        
        # 记录处理开始时间
        import datetime
        start_time = datetime.datetime.now()
        self.stats["last_update"] = start_time.strftime("%Y-%m-%d %H:%M:%S")
        self.stats["total_calls"] += 1
        
        try:
            # 从URL提取文件名
            filename = image_url.split('/')[-1].split('?')[0] or 'url_image'
            # 判断是否是PDF文件
            is_pdf = filename.lower().endswith('.pdf')
            
            # 确定文档类型和API类型
            doc_type, ocr_api_type = self._determine_processing_types(filename, force_type, api_type, False)
            
            # 更新使用统计
            self._update_stats(doc_type, ocr_api_type)
            
            # 调用API
            response = self._call_api(doc_type, ocr_api_type, url=image_url)
            
            # 保存结果
            result = self._save_results(response, output_dir, filename, doc_type, ocr_api_type, is_pdf=is_pdf)
            
            return result
            
        except Exception as e:
            logger.error(f"处理失败 {image_url}: {str(e)}", exc_info=True)
            
            # 记录错误统计
            error_type = str(type(e).__name__)
            if 'doc_type' in locals() and 'ocr_api_type' in locals():
                self._update_stats(doc_type, ocr_api_type, error=e)
            
            return {
                "status": "error",
                "message": f"处理失败 {image_url}: {str(e)}",
                "doc_type": getattr(doc_type, 'value', '未确定') if 'doc_type' in locals() else '未确定',
                "api_type": getattr(ocr_api_type, 'value', '未确定') if 'ocr_api_type' in locals() else '未确定'
            }

    def _extract_text_content(self, data):
        """
        从响应中提取有用的文本内容
        """
        if not data or not isinstance(data, dict):
            return "无法提取文本内容"
            
        content = []
        
        # 常见字段处理映射
        field_handlers = {
            'content': lambda v: content.append(v),
            'blocks': lambda blocks: content.extend([block.get('text', '') for block in blocks if 'text' in block]),
            'prism_wordsInfo': lambda words: content.extend([word.get('word', '') for word in words if 'word' in word])
        }
        
        # 处理常见字段
        for field, handler in field_handlers.items():
            if field in data and data[field]:
                handler(data[field])
        
        # 特殊处理表格内容
        if 'tables' in data and data['tables']:
            content.append("\n--- 表格内容 ---\n")
            for table in data['tables']:
                if 'cells' in table:
                    for cell in table['cells']:
                        if 'text' in cell:
                            content.append(f"{cell.get('row', '-')}行{cell.get('col', '-')}列: {cell['text']}")
        
        # 处理特定字段
        special_fields = {
            'invoice_data': "票据信息",
            'business_license': "营业执照信息"
        }
        
        for field, description in special_fields.items():
            if field in data and data[field]:
                content.append(f"\n--- {description} ---\n")
                for key, value in data[field].items():
                    content.append(f"{key}: {value}")
        
        # 如果没有提取到内容，尝试转换整个响应
        if not content and isinstance(data, dict):
            content.append(json.dumps(data, ensure_ascii=False, indent=2))
        
        return "\n".join(content)

    def process_directory(self, directory_path, output_dir=None, force_type=None, basic_pdf=False, api_type=None, force_general=False):
        """
        批量处理目录中的所有文件
        """
        directory = Path(directory_path)
        if not directory.exists() or not directory.is_dir():
            print(f"错误: 目录 {directory_path} 不存在或不是有效目录")
            return []
        
        # 确保输出目录存在
        if output_dir is None:
            output_dir = self.config.get("default_output_dir", "ocr_results")
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 收集所有支持的文件
        supported_extensions = ['.jpg', '.jpeg', '.png', '.pdf', '.xlsx', '.xls', '.bmp', '.tiff', '.tif']
        files = []
        for ext in supported_extensions:
            files.extend(list(directory.glob(f"**/*{ext}")))
        
        if not files:
            print(f"警告: 在目录 {directory_path} 中未找到支持的文件格式")
            return []
        
        print(f"找到 {len(files)} 个支持的文件")
        results = []
        
        # 处理每个文件
        for i, file_path in enumerate(files):
            print(f"\n处理文件 {i+1}/{len(files)}: {file_path}")
            
            try:
                # 使用process_file_with_feedback处理文件
                result = process_file_with_feedback(
                    self,
                    str(file_path),
                    output_dir,
                    force_type,
                    basic_pdf,
                    api_type,
                    force_general
                )
                
                # 显示简洁反馈
                if "feedback" in result:
                    print(f"✓ {result['feedback']['message']}")
                
                results.append(result)
            except Exception as e:
                print(f"处理 {file_path} 时出错: {str(e)}")
                # 记录错误信息
                results.append({
                    "status": "error",
                    "file_path": str(file_path),
                    "error": str(e)
                })
        
        # 汇总统计数据
        success_count = sum(1 for r in results if r.get("status") == "success")
        error_count = len(results) - success_count
        
        print(f"\n批处理摘要:")
        print(f"总共处理: {len(results)} 个文件")
        print(f"成功: {success_count} 个文件")
        if error_count > 0:
            print(f"失败: {error_count} 个文件")
        
        return results

    def get_stats(self):
        """
        获取API使用统计
        :return: 使用统计字典
        """
        # 返回API使用统计信息
        return {
            "总使用次数": sum(self.stats["api_type_counts"].values()),
            "各类型使用详情": self.stats["doc_type_counts"],
            "API类型使用详情": self.stats["api_type_counts"],
            "错误统计": self.stats["errors"],
            "总调用次数": self.stats["total_calls"],
            "上次更新时间": self.stats["last_update"]
        }

    def show_usage_stats(self):
        """显示API使用统计信息"""
        print("\n==== API使用统计 ====")
        
        # 初始化计数器
        total_uses = 0
        type_stats = {}
        api_stats = {}
        
        # 统计各类型和API的使用次数
        for doc_type, count in self.stats["doc_type_counts"].items():
            type_stats[doc_type] = count
            total_uses += count
            
        for api_type, count in self.stats["api_type_counts"].items():
            api_stats[api_type] = count
            
        # 显示统计结果
        print(f"总计调用: {total_uses} 次")
        
        print("\n文档类型使用统计:")
        if type_stats:
            for doc_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_uses) * 100 if total_uses > 0 else 0
                print(f"  {doc_type}: {count} 次 ({percentage:.1f}%)")
        else:
            print("  暂无统计数据")
            
        print("\nAPI类型使用统计:")
        if api_stats:
            for api_type, count in sorted(api_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_uses) * 100 if total_uses > 0 else 0
                print(f"  {api_type}: {count} 次 ({percentage:.1f}%)")
        else:
            print("  暂无统计数据")
            
        # 显示错误统计
        print("\n错误统计:")
        errors = self.stats["errors"]
        if errors:
            for error_type, count in sorted(errors.items(), key=lambda x: x[1], reverse=True):
                print(f"  {error_type}: {count} 次")
        else:
            print("  暂无错误记录")
            
        print("\n统计记录已保存至配置目录")

def get_tool():
    """获取OCR工具实例"""
    try:
        return OCRTool()
    except Exception as e:
        error_msg = str(e)
        if "找不到配置文件" in error_msg or isinstance(e, FileNotFoundError):
            raise ValueError("OCR工具配置文件不存在，请确保config.json文件已正确设置")
        elif "无效的API密钥" in error_msg or isinstance(e, ValueError):
            raise ValueError("OCR工具配置无效，请检查API密钥和设置")
        else:
            raise Exception(f"初始化OCR工具失败: {error_msg}")

def show_help():
    """显示帮助信息"""
    print("""
小米智能秘书OCR工具 - 文档识别助手
==================================

使用方法:
    python ocr_tool.py [选项] <输入路径>
    python ocr_tool.py --image_path <图片路径> [其他选项]
    
输入路径:
    可以是单个文件路径、目录路径或URL

选项:
    --help, -h            显示此帮助信息
    --image_path <路径>    指定要处理的图片或文档路径
    --batch               批量处理模式 (输入路径必须是目录)
    --output_dir <路径>    指定输出目录 (默认在当前目录创建ocr_results)
    --force_type <类型>    强制指定文档类型:
                          invoice(发票)、receipt(收据)、contract(合同)、license(营业执照)、
                          idcard(身份证)、vat(增值税发票)、form(表格)、general(通用文字)
    --api_type <类型>      强制指定API类型:
                          basic(基础版)、advanced(高精版)、handwriting(手写体)、
                          unified(统一识别)、table(表格识别)
    --basic_pdf           使用基本PDF处理模式 (适用于简单的PDF文档)
    --force_general       强制使用通用文字识别API (适用于无法被正确识别类型的文档)
    --stats               显示API使用统计信息

注意: 所有参数支持下划线(_)和短横线(-)两种形式，例如 --output_dir 和 --output-dir 都有效

示例:
    # 处理单个文件
    python ocr_tool.py invoice.jpg
    
    # 使用--image_path参数指定文件
    python ocr_tool.py --image_path receipt.pdf --output_dir ./results
    
    # 批量处理目录下的文件
    python ocr_tool.py --batch ./documents --output_dir ./results
    
    # 处理网络图片
    python ocr_tool.py https://example.com/document.jpg
    
    # 强制使用特定文档类型和API
    python ocr_tool.py --image_path contract.pdf --force_type contract --api_type advanced
    
    # 查看使用统计
    python ocr_tool.py --stats

支持的文件格式:
    图像: .jpg, .jpeg, .png, .bmp, .tiff, .tif
    文档: .pdf
    表格: .xlsx, .xls
    
输出格式:
    图像文件 → 输出JSON格式
    PDF文件  → 输出JSON格式
    Excel文件 → 输出CSV格式
    """)

def process_file_with_feedback(ocr, input_path, output_dir, force_type, basic_pdf, api_type, force_general):
    """处理单个文件并提供反馈"""
    file_path = Path(input_path)
    file_ext = file_path.suffix.lower()
    
    result = ocr.process_file(
        input_path, 
        output_dir=output_dir, 
        force_type=force_type,
        basic_pdf=basic_pdf,
        api_type=api_type,
        force_general=force_general
    )
    
    # 为批处理添加文件路径，方便统计
    result["file_path"] = str(file_path)
    
    # 生成用户反馈信息
    if result["status"] == "success":
        feedback = {
            "message": f"处理成功: {file_path}",
            "details": []
        }
        
        # 根据文件类型添加特定的输出信息
        if file_ext in ['.xlsx', '.xls']:
            feedback["type_message"] = "Excel文件已转换为CSV格式"
            if "csv_results" in result:
                for csv_path in result.get("csv_results", []):
                    feedback["details"].append(f"- 输出: {csv_path}")
        elif file_ext == '.pdf':
            feedback["type_message"] = "PDF文件已输出为JSON格式"
            if "json_result" in result:
                feedback["details"].append(f"- 输出: {result['json_result']}")
            if "multi_page_results" in result and result["multi_page_results"]:
                feedback["details"].append(f"- 多页结果: {len(result['multi_page_results'])}页")
        else:
            feedback["type_message"] = "图像文件已输出为JSON格式"
            if "json_result" in result:
                feedback["details"].append(f"- 输出: {result['json_result']}")
        
        result["feedback"] = feedback
    
    return result

def process_batch(ocr, params):
    """处理批量模式"""
    results = ocr.process_directory(
        params["input_path"], 
        output_dir=params["output_dir"], 
        force_type=params["force_type"],
        basic_pdf=params["basic_pdf"],
        api_type=params["api_type"],
        force_general=params["force_general"]
    )
    print(f"\n批量处理完成，共处理 {len(results)} 个文件")
    
    # 计算各种类型文件数量
    pdf_count = sum(1 for r in results if getattr(Path(r.get("file_path", "")), "suffix", "").lower() == '.pdf')
    excel_count = sum(1 for r in results if getattr(Path(r.get("file_path", "")), "suffix", "").lower() in ['.xlsx', '.xls'])
    image_count = len(results) - pdf_count - excel_count
    
    if pdf_count > 0:
        print(f"- {pdf_count} 个PDF文件 (输出为JSON格式)")
    if excel_count > 0:
        print(f"- {excel_count} 个Excel文件 (输出为CSV格式)")
    if image_count > 0:
        print(f"- {image_count} 个图像文件 (输出为JSON格式)")
    
    # 显示处理结果统计
    success_count = sum(1 for r in results if r.get("status") == "success")
    error_count = len(results) - success_count
    if error_count > 0:
        print(f"\n处理失败: {error_count} 个文件")
    
    print(f"\n所有结果已保存到: {params['output_dir'] or '默认输出目录'}")

def main():
    """主程序入口"""
    args = sys.argv[1:]
    
    # 在无参数情况下显示帮助信息
    if not args or "--help" in args or "-h" in args:
        show_help()
        return
    
    # 初始化参数默认值
    params = {
        "input_path": None,
        "output_dir": None,
        "force_type": None,
        "basic_pdf": False,
        "api_type": None,
        "force_general": False,
        "batch_mode": False,
        "show_stats": False
    }
    
    # 解析命令行参数
    i = 0
    while i < len(args):
        arg = args[i]
        
        if arg in ["--output_dir", "--output-dir"] and i + 1 < len(args):
            params["output_dir"] = args[i + 1]
            i += 2
        elif arg in ["--force_type", "--force-type"] and i + 1 < len(args):
            params["force_type"] = args[i + 1]
            i += 2
        elif arg in ["--api_type", "--api-type"] and i + 1 < len(args):
            params["api_type"] = args[i + 1]
            i += 2
        elif arg in ["--image_path", "--image-path"] and i + 1 < len(args):
            params["input_path"] = args[i + 1]
            i += 2
        elif arg in ["--basic_pdf", "--basic-pdf"]:
            params["basic_pdf"] = True
            i += 1
        elif arg in ["--force_general", "--force-general"]:
            params["force_general"] = True
            i += 1
        elif arg == "--batch":
            params["batch_mode"] = True
            i += 1
        elif arg == "--stats":
            params["show_stats"] = True
            i += 1
        elif params["input_path"] is None:
            params["input_path"] = arg
            i += 1
        else:
            print(f"警告: 忽略未知参数 {arg}")
            i += 1
    
    # 检查是否需要显示统计信息
    if params["show_stats"]:
        try:
            ocr = get_tool()
            ocr.show_usage_stats()
            return
        except Exception as e:
            print(f"显示统计信息失败: {str(e)}")
            return
    
    # 检查必须的输入路径
    if not params["input_path"]:
        print("错误: 未指定输入路径")
        show_help()
        return
    
    # 显示处理模式信息
    path_type = "目录" if params["batch_mode"] else "文件或URL"
    print(f"处理{path_type}: {params['input_path']}")
    
    if params["output_dir"]:
        print(f"输出目录: {params['output_dir']}")
    if params["force_type"]:
        print(f"强制文档类型: {params['force_type']}")
    if params["api_type"]:
        print(f"强制API类型: {params['api_type']}")
    if params["basic_pdf"]:
        print("使用基本PDF处理模式")
    if params["force_general"]:
        print("强制使用通用文字识别")
    
    # 初始化OCR工具
    try:
        ocr = get_tool()
    except Exception as e:
        print(f"初始化OCR工具失败: {str(e)}")
        return
    
    # 根据模式处理文件
    try:
        if params["batch_mode"]:
            process_batch(ocr, params)
        else:
            # 检查是否为URL
            if params["input_path"].startswith("http://") or params["input_path"].startswith("https://"):
                process_url(ocr, params)
            else:
                process_single_file(ocr, params)
    except Exception as e:
        print(f"处理失败: {str(e)}")
        traceback.print_exc()

def process_url(ocr, params):
    """处理URL模式"""
    url = params["input_path"]
    print(f"\n处理URL: {url}")
    print("下载中...")
    
    try:
        result = ocr.process_url(
            url, 
            output_dir=params["output_dir"], 
            force_type=params["force_type"],
            api_type=params["api_type"]
        )
        
        if result["status"] == "success":
            print("\n处理成功:")
            
            # 根据文件类型显示不同提示
            filename = url.split('/')[-1].lower()
            
            if filename.endswith(('.xlsx', '.xls')):
                print("Excel文件已转换为CSV格式")
                if "csv_results" in result:
                    for csv_path in result.get("csv_results", []):
                        print(f"- 输出: {csv_path}")
            elif filename.endswith('.pdf'):
                print("PDF文件已输出为JSON格式")
                if "json_result" in result:
                    print(f"- 输出: {result['json_result']}")
                if "multi_page_results" in result and result["multi_page_results"]:
                    print(f"- 多页结果: {len(result['multi_page_results'])}页")
            else:
                print("图像/文件已输出为JSON格式")
                if "json_result" in result:
                    print(f"- 输出: {result['json_result']}")
            
        else:
            print(f"\n处理失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"处理URL失败: {str(e)}")
        traceback.print_exc()

def process_single_file(ocr, params):
    """处理单文件模式"""
    result = process_file_with_feedback(
        ocr,
        params["input_path"],
        params["output_dir"],
        params["force_type"],
        params["basic_pdf"],
        params["api_type"],
        params["force_general"]
    )
    
    if "feedback" in result:
        feedback = result["feedback"]
        print(f"\n{feedback['message']}")
        print(feedback["type_message"])
        for detail in feedback["details"]:
            print(detail)

if __name__ == "__main__":
    main()
