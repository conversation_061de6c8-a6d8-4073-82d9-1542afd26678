# -*- text -*-
#
#	Attributes and values defined in RFC 2865.
#	http://www.ietf.org/rfc/rfc2865.txt
#
#	$Id$
#
ATTRIBUTE	User-Name				1	string
ATTRIBUTE	User-Password				2	string
ATTRIBUTE	CHAP-Password				3	octets
ATTRIBUTE	NAS-IP-Address				4	ipaddr
ATTRIBUTE	NAS-Port				5	integer
ATTRIBUTE	Service-Type				6	integer
ATTRIBUTE	Framed-Protocol				7	integer
ATTRIBUTE	Framed-IP-Address			8	ipaddr
ATTRIBUTE	Framed-IP-Netmask			9	ipaddr
ATTRIBUTE	Framed-Routing				10	integer
ATTRIBUTE	Filter-Id				11	string
ATTRIBUTE	Framed-MTU				12	integer
ATTRIBUTE	Framed-Compression			13	integer
ATTRIBUTE	Login-IP-Host				14	ipaddr
ATTRIBUTE	Login-Service				15	integer
ATTRIBUTE	Login-TCP-Port				16	integer
# Attribute 17 is undefined
ATTRIBUTE	Reply-Message				18	string
ATTRIBUTE	Callback-Number				19	string
ATTRIBUTE	Callback-Id				20	string
# Attribute 21 is undefined
ATTRIBUTE	Framed-Route				22	string
ATTRIBUT<PERSON>	Framed-IPX-Network			23	ipaddr
ATTRIBUTE	State					24	octets
ATTRIBUTE	Class					25	octets
ATTRIBUTE	Vendor-<PERSON>pecific				26	octets
ATTRIBUTE	Session-Timeout				27	integer
ATTRIBUTE	Idle-Timeout				28	integer
ATTRIBUTE	Termination-Action			29	integer
ATTRIBUTE	Called-Station-Id			30	string
ATTRIBUTE	Calling-Station-Id			31	string
ATTRIBUTE	NAS-Identifier				32	string
ATTRIBUTE	Proxy-State				33	octets
ATTRIBUTE	Login-LAT-Service			34	string
ATTRIBUTE	Login-LAT-Node				35	string
ATTRIBUTE	Login-LAT-Group				36	octets
ATTRIBUTE	Framed-AppleTalk-Link			37	integer
ATTRIBUTE	Framed-AppleTalk-Network		38	integer
ATTRIBUTE	Framed-AppleTalk-Zone			39	string

ATTRIBUTE	CHAP-Challenge				60	octets
ATTRIBUTE	NAS-Port-Type				61	integer
ATTRIBUTE	Port-Limit				62	integer
ATTRIBUTE	Login-LAT-Port				63	string

#
#	Integer Translations
#

#	Service types

VALUE	Service-Type			Login-User		1
VALUE	Service-Type			Framed-User		2
VALUE	Service-Type			Callback-Login-User	3
VALUE	Service-Type			Callback-Framed-User	4
VALUE	Service-Type			Outbound-User		5
VALUE	Service-Type			Administrative-User	6
VALUE	Service-Type			NAS-Prompt-User		7
VALUE	Service-Type			Authenticate-Only	8
VALUE	Service-Type			Callback-NAS-Prompt	9
VALUE	Service-Type			Call-Check		10
VALUE	Service-Type			Callback-Administrative	11

#	Framed Protocols

VALUE	Framed-Protocol			PPP			1
VALUE	Framed-Protocol			SLIP			2
VALUE	Framed-Protocol			ARAP			3
VALUE	Framed-Protocol			Gandalf-SLML		4
VALUE	Framed-Protocol			Xylogics-IPX-SLIP	5
VALUE	Framed-Protocol			X.75-Synchronous	6

#	Framed Routing Values

VALUE	Framed-Routing			None			0
VALUE	Framed-Routing			Broadcast		1
VALUE	Framed-Routing			Listen			2
VALUE	Framed-Routing			Broadcast-Listen	3

#	Framed Compression Types

VALUE	Framed-Compression		None			0
VALUE	Framed-Compression		Van-Jacobson-TCP-IP	1
VALUE	Framed-Compression		IPX-Header-Compression	2
VALUE	Framed-Compression		Stac-LZS		3

#	Login Services

VALUE	Login-Service			Telnet			0
VALUE	Login-Service			Rlogin			1
VALUE	Login-Service			TCP-Clear		2
VALUE	Login-Service			PortMaster		3
VALUE	Login-Service			LAT			4
VALUE	Login-Service			X25-PAD			5
VALUE	Login-Service			X25-T3POS		6
VALUE	Login-Service			TCP-Clear-Quiet		8

#	Login-TCP-Port		(see /etc/services for more examples)

VALUE	Login-TCP-Port			Telnet			23
VALUE	Login-TCP-Port			Rlogin			513
VALUE	Login-TCP-Port			Rsh			514

#	Termination Options

VALUE	Termination-Action		Default			0
VALUE	Termination-Action		RADIUS-Request		1

#	NAS Port Types

VALUE	NAS-Port-Type			Async			0
VALUE	NAS-Port-Type			Sync			1
VALUE	NAS-Port-Type			ISDN			2
VALUE	NAS-Port-Type			ISDN-V120		3
VALUE	NAS-Port-Type			ISDN-V110		4
VALUE	NAS-Port-Type			Virtual			5
VALUE	NAS-Port-Type			PIAFS			6
VALUE	NAS-Port-Type			HDLC-Clear-Channel	7
VALUE	NAS-Port-Type			X.25			8
VALUE	NAS-Port-Type			X.75			9
VALUE	NAS-Port-Type			G.3-Fax			10
VALUE	NAS-Port-Type			SDSL			11
VALUE	NAS-Port-Type			ADSL-CAP		12
VALUE	NAS-Port-Type			ADSL-DMT		13
VALUE	NAS-Port-Type			IDSL			14
VALUE	NAS-Port-Type			Ethernet		15
VALUE	NAS-Port-Type			xDSL			16
VALUE	NAS-Port-Type			Cable			17
VALUE	NAS-Port-Type			Wireless-Other		18
VALUE	NAS-Port-Type			Wireless-802.11		19
