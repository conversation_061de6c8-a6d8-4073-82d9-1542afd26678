# NOTE, same format as dynamic_0  It is slower (50% slower, or more).  But it is not limited to 55 byte passwords.
# This should work for passwords up to 110 bytes long (max length dynamic will currently allow). It should not be
# used for shorter passwords (under 55 bytes).  Use dyna_0 for those.
[List.Generic:dynamic_2000]
Expression=md5($p) (PW > 55 bytes)
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_INPUT
Flag=MGF_SOURCE
Flag=MGF_POOR_OMP
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_2000$5a105e8b9d40e1329780d62ea2265d8a:test1
Test=$dynamic_2000$378e2c4a07968da2eca692320136433d:thatsworking
Test=$dynamic_2000$8ad8757baa8564dc136c1e07507f4a98:test3
TestD=$dynamic_2000$a4b3933521a38111eb597dd8dbc47614:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2001]
Expression=md5($p.$s) (joomla) (PW > 23 bytes)
Flag=MGF_FLAT_BUFFERS
Flag=MGF_SALTED
SaltLen=-64
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_2001$ed52af63d8ecf0c682442dfef5f36391$1aDNNojYGSc7pSzcdxKxhbqvLtEe4deG:test1
Test=$dynamic_2001$4fa1e9d54d89bfbe48b4c0f0ca0a3756$laxcaXPjgcdKdKEbkX1SIjHKm0gfYt1c:thatsworking
Test=$dynamic_2001$82568eeaa1fcf299662ccd59d8a12f54$BdWwFsbGtXPGc0H1TBxCrn0GasyAlJBJ:test3
TestD=$dynamic_2001$a4d4ce08d9dec5336d2a137cdab28624$1234567890123456789012345678901234567890123456789012345678901234:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2002]
Expression=md5(md5($p)) (e107) (PW > 55 bytes)
Flag=MGF_KEYS_INPUT
Flag=MGF_FLAT_BUFFERS
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__MD5_crypt_input1_overwrite_input2
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_2002$418d89a45edadb8ce4da17e07f72536c:test1
Test=$dynamic_2002$ccd3c4231a072b5e13856a2059d04fad:thatsworking
Test=$dynamic_2002$9992295627e7e7162bdf77f14734acf8:test3
TestD=$dynamic_2002$827b31e7fae2cdb3af70be9560162500:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2003]
Expression=md5(md5(md5($p))) (PW > 55 bytes)
Flag=MGF_KEYS_INPUT
Flag=MGF_FLAT_BUFFERS
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__MD5_crypt_input1_overwrite_input2
Func=DynamicFunc__MD5_crypt_input2_overwrite_input2
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_2003$964c02612b2a1013ed26d46ba9a73e74:test1
Test=$dynamic_2003$5d7e6330f69548797c07d97c915690fe:thatsworking
Test=$dynamic_2003$2e54db8c72b312007f3f228d9d4dd34d:test3
TestD=$dynamic_2003$35297f9d34baa8e3ca3e5b23155be26f:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2004]
Expression=md5($s.$p) (OSC) (PW > 31 bytes)
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
SaltLen=-64
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_2004$c02e8eef3eaa1a813c2ff87c1780f9ed$123456:test1
Test=$dynamic_2004$4a2a1b013da3cda7f7e0625cf3dc3f4c$1234:thatsworking
Test=$dynamic_2004$3a032e36a9609df6411b8004070431d3$aaaaa:test3
TestD=$dynamic_2004$d75040e824c1f9e4efd67c19961ddf4d$1234567890123456789012345678901234567890123456789012345678901234:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2005]
Expression=md5($s.$p.$s) (PW > 31 bytes)
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
SaltLen=-40
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_2005$c1003cd39cb5523dd0923a94ab15a3c7$123456:test1
Test=$dynamic_2005$c1c8618abfc7bdbc4a3c49c2c2c48f82$1234:thatsworking
Test=$dynamic_2005$e7222e806a8ce5efa6d48acb3aa56dc2$aaaaa:test3
TestD=$dynamic_2005$ba5528ac65c20213e105bb02e6aaf6a2$1234567890123456789012345678901234567890:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2006]
Expression=md5(md5($p).$s) (PW > 55 bytes)
Flag=MGF_SALTED
Flag=MGF_KEYS_BASE16_IN1
Flag=MGF_FLAT_BUFFERS
SaltLen=-64
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__set_input_len_32
Func=DynamicFunc__append_salt
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_2006$3a9ae23758f05da1fe539e55a096b03b$S111XB:test1
Test=$dynamic_2006$9694d706d1992abf04344c1e7da1c5d3$T &222:thatsworking
Test=$dynamic_2006$b7a7f0c374d73fac422bb01f07f5a9d4$lxxxl:test3
Test=$dynamic_2006$9164fe53be481f811f15efd769aaf0f7$aReallyLongSaltHere:test3
TestD=$dynamic_2006$7308f7ca156d77564a5dab25d4be0f34$1234567890123456789012345678901234567890123456789012345678901234:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2008]
Expression=md5(md5($s).$p) (PW > 23 bytes)
Flag=MGF_SALTED
Flag=MGF_SALT_AS_HEX
Flag=MGF_FLAT_BUFFERS
SaltLen=-64
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_2008$534c2fb38e757d9448315abb9822db00$aaaSXB:test1
Test=$dynamic_2008$02547864bed278658e8f54dd6dfd69b7$123456:thatsworking
Test=$dynamic_2008$2f6f3881972653ebcf86e5ad3071a4ca$5555hh:test3
TestD=$dynamic_2008$a96d6ab818950bafc6baeaa80df5ec5c$1234567890123456789012345678901234567890123456789012345678901234:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2009]
Expression=md5($s.md5($p)) (salt > 23 bytes)
Flag=MGF_SALTED
Flag=MGF_KEYS_BASE16_IN1
Flag=MGF_FLAT_BUFFERS
SaltLen=-200
MaxInputLenX86=40
MaxInputLen=40
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_input2_from_input
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_2009$b38c18b5e5b676e211442bd41000b2ec$aaaSXB:test1
Test=$dynamic_2009$4dde7cd4cbf0dc4c59b255ae77352914$123456:thatsworking
Test=$dynamic_2009$899af20e3ebdd77aaecb0d9bc5fbbb66$5555hh:test3

[List.Generic:dynamic_2010]
Expression=md5($s.md5($s.$p)) (PW > 32 or salt > 23 bytes)
Flag=MGF_SALTED
#Flag=MGF_KEYS_BASE16_IN1
Flag=MGF_FLAT_BUFFERS
SaltLen=-64
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__MD5_crypt_input1_append_input2
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_2010$781f83a676f45169dcfc7f36dfcdc3d5$aaaSXB:test1
Test=$dynamic_2010$f385748e67a2dc1f6379b9124fabc0df$123456:thatsworking
Test=$dynamic_2010$9e3702bb13386270cd4b0bd4dbdd489e$5555hh:test3
TestD=$dynamic_2010$74fe90a89e9e6ee5ea28d4a92640eda5$1234567890123456789012345678901234567890123456789012345678901234:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_2011]
Expression=md5($s.md5($p.$s)) (PW > 32 or salt > 23 bytes)
Flag=MGF_SALTED
#Flag=MGF_KEYS_BASE16_IN1
Flag=MGF_FLAT_BUFFERS
SaltLen=-64
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_keys
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_salt
Func=DynamicFunc__MD5_crypt_input1_append_input2
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_2011$f809a64cbd0d23e099cd5b544c8501ac$aaaSXB:test1
Test=$dynamic_2011$979e6671535cda6db95357d8a0afd9ac$123456:thatsworking
Test=$dynamic_2011$78a61ea73806ebf27bef2ab6a9bf5412$5555hh:test3
TestD=$dynamic_2011$d5acc2492e19cbf252d54942b4c7620b$1234567890123456789012345678901234567890123456789012345678901234:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

#[List.Generic:dynamic_2012]
#dynamic_12 already in MGF_FLAT_BUFFERS, so no reason for dynamic_2012

#[List.Generic:dynamic_2013]
#dynamic_13 already in MGF_FLAT_BUFFERS, so no reason for dynamic_2013

[List.Generic:dynamic_2014]
Expression=md5($s.md5($p).$s) (PW > 55 or salt > 11 bytes)
Flag=MGF_SALTED
Flag=MGF_KEYS_BASE16_IN1
Flag=MGF_FLAT_BUFFERS
SaltLen=-40
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_input2_from_input
Func=DynamicFunc__append_salt2
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_2014$778e40e10d82a08f5377992330008cbe$aaaSXB:test1
Test=$dynamic_2014$d6321956964b2d27768df71d139eabd2$123456:thatsworking
Test=$dynamic_2014$1b3c72e16427a2f4f0819243877f7967$5555hh:test3
TestD=$dynamic_2014$6f20299d2e889eea146d141e92e91da1$1234567890123456789012345678901234567890:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

#[List.Generic:dynamic_2015]
#dynamic_15 already in MGF_FLAT_BUFFERS, so no reason for dynamic_2015

#[List.Generic:dynamic_2016]
#dynamic_16 already in MGF_FLAT_BUFFERS, so no reason for dynamic_2016
