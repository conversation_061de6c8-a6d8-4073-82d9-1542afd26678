
import cv2
import os
import numpy as np
from gfpgan import GFPGANer

def restore_face():
    # --- 配置 ---
    # 模型版本，'GFPGANv1.3' 是一个常用的稳定版本
    model_name = 'GFPGANv1.3'
    # 输入图片路径
    input_path = r"E:\Projects\PlayGemini\mago\请把我牵走\qbwdz009.JPG"
    # 输出图片路径
    output_path = r"E:\Projects\PlayGemini\mago\请把我牵走\qbwdz009_restored.JPG"

    print(f"正在处理图片: {input_path}")

    # --- 初始化模型 ---
    # 检查模型文件是否存在，如果不存在，GFPGANer会自动下载
    # upscale: 放大倍数，这里我们只做修复，不做放大，所以是1
    # arch: 'clean' 表示使用常规的GFPGAN模型
    # channel_multiplier: 通道倍增器，2是默认值
    # bg_upsampler: 背景的放大器，我们不需要，设为None
    try:
        restorer = GFPGANer(
            model_path=r"E:\Projects\PlayGemini\experiments\pretrained_models\GFPGANv1.3.pth",
            upscale=1,
            arch='clean',
            channel_multiplier=2,
            bg_upsampler=None
        )
    except Exception as e:
        print(f"初始化模型失败，请检查网络连接。错误: {e}")
        # 如果自动下载失败，提示用户手动下载
        print(f"请尝试手动从 https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/{model_name}.pth 下载模型文件")
        print(f"并将其放置在 E:\Projects\PlayGemini\experiments\pretrained_models 文件夹中。")
        return

    # --- 读取图片 ---
    if not os.path.exists(input_path):
        print(f"错误：找不到输入图片路径 {input_path}")
        return
        
    # Use a robust way to read images with non-ASCII paths
    try:
        img = cv2.imdecode(np.fromfile(input_path, dtype=np.uint8), cv2.IMREAD_COLOR)
    except Exception as e:
        print(f"错误：无法使用兼容模式读取图片 {input_path}，错误信息: {e}")
        return
    if img is None:
        print(f"错误：无法读取图片 {input_path}")
        return

    # --- 执行还原 ---
    # has_aligned: False 表示让GFPGAN自动检测和对齐人脸
    # only_center_face: False 表示处理图中的所有脸部
    # paste_back: True 表示将修复后的人脸粘贴回原图
    try:
        cropped_faces, restored_faces, restored_img = restorer.enhance(
            img,
            has_aligned=False,
            only_center_face=False,
            paste_back=True
        )
    except Exception as e:
        print(f"执行人脸还原时出错: {e}")
        return

    # --- 保存结果 ---
    if restored_img is not None:
        try:
            cv2.imwrite(output_path, restored_img)
            print(f"成功！修复后的图片已保存至: {output_path}")
        except Exception as e:
            print(f"保存修复后的图片失败: {e}")
    else:
        print("未检测到人脸或无法修复，没有生成新图片。")

if __name__ == '__main__':
    restore_face()
