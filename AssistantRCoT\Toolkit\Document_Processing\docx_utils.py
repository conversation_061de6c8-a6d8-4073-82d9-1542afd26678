import argparse
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def read_document_text(file_path):
    """
    读取并返回一个Word文档的纯文本内容。
    """
    try:
        doc = Document(file_path)
        full_text = [para.text for para in doc.paragraphs]
        return '\n'.join(full_text)
    except Exception as e:
        return f"读取文件时出错: {e}"

def create_document(file_path, content_list):
    """
    根据结构化内容列表创建一个新的Word文档。

    :param file_path: 输出的Word文档路径。
    :param content_list: 一个字典列表，每个字典代表一个内容元素。
                         例如: [{'type': 'heading', 'level': 1, 'text': '标题一'},
                                {'type': 'paragraph', 'text': '这是一个段落。'}]
    """
    document = Document()
    
    # 设置页边距
    for section in document.sections:
        section.top_margin = Inches(1.0)
        section.bottom_margin = Inches(1.0)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)

    for item in content_list:
        item_type = item.get('type', 'paragraph')
        text = item.get('text', '')
        
        if item_type == 'heading':
            level = item.get('level', 1)
            alignment = item.get('align', 'left')
            font_size = item.get('size', None)
            bold = item.get('bold', False)
            
            heading = document.add_heading(level=level)
            run = heading.add_run(text)
            
            if font_size:
                run.font.size = Pt(font_size)
            if bold:
                run.bold = True
            
            if alignment == 'center':
                heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
            elif alignment == 'right':
                heading.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            else:
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

        elif item_type == 'paragraph':
            alignment = item.get('align', 'left')
            p = document.add_paragraph(text)
            
            if alignment == 'center':
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            elif alignment == 'right':
                p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            else:
                p.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        elif item_type == 'space':
            document.add_paragraph()

    try:
        document.save(file_path)
        return f"文档已成功保存至: {file_path}"
    except Exception as e:
        return f"保存文件时出错: {e}"

def generate_hospital_report_content():
    """
    生成特定医院报告的结构化内容。
    这是对原 generate_report.py 功能的封装。
    """
    return [
        {'type': 'heading', 'level': 0, 'text': '绵阳市涪城区妇女儿童医院项目综合进展报告', 'align': 'center', 'size': 22, 'bold': True},
        {'type': 'space'},
        {'type': 'heading', 'level': 1, 'text': '一、引言'},
        {'type': 'paragraph', 'text': '本报告旨在综合呈现绵阳市涪城区妇女儿童医院近期项目推进情况与计划采购物资信息，并更新最新项目进展。'},
        # ... 此处可以继续添加原报告的所有内容 ...
        {'type': 'heading', 'level': 1, 'text': '五、存在困难问题与拟解决方案'},
        {'type': 'paragraph', 'text': '1.  加强供应链管理：提前与供应商沟通，建立备用采购渠道，确保关键物资供应。'},
        {'type': 'space'},
        {'type': 'paragraph', 'text': '报告单位：[请在此处填写报告单位名称]'},
        {'type': 'paragraph', 'text': '日期：2025年8月1日', 'align': 'right'},
    ]

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="一个通用的Word文档处理工具，支持读写操作。")
    subparsers = parser.add_subparsers(dest='command', help='可用的命令')

    # 创建 "read" 命令
    parser_read = subparsers.add_parser('read', help='读取一个Word文档的内容并打印到控制台。')
    parser_read.add_argument('filepath', help='要读取的.docx文件路径')

    # 创建 "create" 命令
    parser_create = subparsers.add_parser('create', help='创建一个新的Word文档。')
    parser_create.add_argument('filepath', help='要创建的.docx文件路径')
    parser_create.add_argument('--title', help='文档的标题')
    parser_create.add_argument('--paragraph', help='文档的段落内容')
    
    # 创建 "create-report" 命令，用于演示旧功能
    parser_create_report = subparsers.add_parser('create-report', help='创建特定的医院项目进展报告。')
    parser_create_report.add_argument('filepath', help='要创建的报告文件路径')

    args = parser.parse_args()

    if args.command == 'read':
        print(read_document_text(args.filepath))
    elif args.command == 'create':
        content = []
        if args.title:
            content.append({'type': 'heading', 'level': 1, 'text': args.title})
        if args.paragraph:
            content.append({'type': 'paragraph', 'text': args.paragraph})
        if not content:
            print("错误：创建文档需要至少一个 --title 或 --paragraph 参数。")
        else:
            print(create_document(args.filepath, content))
    elif args.command == 'create-report':
        report_content = generate_hospital_report_content()
        print(create_document(args.filepath, report_content))
    else:
        parser.print_help()