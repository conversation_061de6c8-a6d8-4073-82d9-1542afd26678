# regex_alphabets.conf
#
#  This is the multiple alphabest usable by rexgen function.

# this is the 'default'
[List.Rexgen.Alpha]

# can also use -i mode rexgen (TBD)
[List.Rexgen.Alpha:cased]
a=[aA]
b=[bB]
c=[cC]
d=[dD]
e=[eE]
f=[fF]
g=[gG]
h=[hH]
i=[iI]
j=[jJ]
k=[kK]
l=[lL]
m=[mM]
n=[nN]
o=[oO]
p=[pP]
q=[qQ]
r=[rR]
s=[sS]
t=[tT]
u=[uU]
v=[vV]
w=[wW]
x=[xX]
y=[yY]
z=[zZ]
A=[aA]
B=[bB]
C=[cC]
D=[dD]
E=[eE]
F=[fF]
G=[gG]
H=[hH]
I=[iI]
J=[jJ]
K=[kK]
L=[lL]
M=[mM]
N=[nN]
O=[oO]
P=[pP]
Q=[qQ]
R=[rR]
S=[sS]
T=[tT]
U=[uU]
V=[vV]
W=[wW]
X=[xX]
Y=[yY]
Z=[zZ]

# simple 1337 mode.  ONLY leet's lower case letters, and smallish alphabet. But VERY fast.
[List.Rexgen.Alpha:leet]
a=[a4@]
b=[b8]
e=[e3]
g=[g9]
i=[i!]
l=[l17]
o=[o0]
s=[s$5]
t=[t+7]

# simple 1337 mode with mixed case
[List.Rexgen.Alpha:leet+c]
a=[aA4@]
b=[bB8]
c=[cC]
d=[dD]
e=[eE3]
f=[fF]
g=[gG9]
h=[hH]
i=[iI!]
j=[jJ]
k=[kK]
l=[lL1]
m=[mM]
n=[nN]
o=[oO0]
p=[pP]
q=[qQ]
r=[rR]
s=[sS$5]
t=[tT+7]
u=[uU]
v=[vV]
w=[wW]
x=[xX]
y=[yY]
z=[zZ]
A=[aA]
B=[bB]
C=[cC]
D=[dD]
E=[eE]
F=[fF]
G=[gG]
H=[hH]
I=[iI]
J=[jJ]
K=[kK]
L=[lL]
M=[mM]
N=[nN]
O=[oO]
P=[pP]
Q=[qQ]
R=[rR]
S=[sS]
T=[tT]
U=[uU]
V=[vV]
W=[wW]
X=[xX]
Y=[yY]
Z=[zZ]

# much stronger 1337 mode.  Does much larger alphabet.  Includes a couple multiple
# character replacement values: f -> ph and f -> |=  Also does upper case
# note contains ALL values from Rexgen.Alpha:leet
[List.Rexgen.Alpha:leet2]
a=[a4@]
b=[b8]
c=[c\(<k]
e=[e3]
f=(f|ph|\|=)
g=[g9]
i=[i1!\|]
l=[l1]
o=[o0]
s=[s$5]
t=[t+7]
A=[A4@]
B=[B8]
C=[C\(<k]
E=[E3]
F=(F|Ph|PH|\|=)
G=[G9]
I=[I1!\|]
L=[L1]
O=[O0]
S=[S$5]
T=[T+7]

# stronger elete, with mixed case.
[List.Rexgen.Alpha:leet2_case]
a=[aA4@]
b=[bB8]
c=[cC\(]
d=[dD]
e=[eE3]
f=(f|F|ph|Ph|PH|\|=)
g=[gG9]
h=[hH]
i=[iI1!\|]
j=[jJ]
k=[kK]
l=[lL1]
m=[mM]
n=[nN]
o=[oO0]
p=[pP]
q=[qQ]
r=[rR]
s=[sS$5]
t=[tT+7]
u=[uU]
v=[vV]
w=[wW]
x=[xX]
y=[yY]
z=[zZ]
A=[aA4@]
B=[bB8]
C=[cC\(]
D=[dD]
E=[eE3]
F=(f|F|Ph|ph|PH|\|=)
G=[gG9]
H=[hH]
I=[iI1!\|]
J=[jJ]
K=[kK]
L=[lL1]
M=[mM]
N=[nN]
O=[oO0]
P=[pP]
Q=[qQ]
R=[rR]
S=[sS$5]
T=[tT+7]
U=[uU]
V=[vV]
W=[wW]
X=[xX]
Y=[yY]
Z=[zZ]

# Very strong elete.  MANY multi char eletes, AND some other more obsure ones.
# a LOT of stuff here, BUT runs much much slower, since there are many more optional
# values to try.
# note contains ALL values from Rexgen.Alpha:leet2
[List.Rexgen.Alpha:leet3]
a=(a|/-\\|4|@)
b=(b|\|3|\|o|8)
c=[c\(<KS]
d=(d|\|\)|o\||\|>|<\|)
e=[e3]
f=(f|ph|\|=)
g=[g\(69]
h=(h|\|\-\||\]\-\[|\}-\{|\(-\)|\)-\(|\}\{|#)
i=(i|1|!|\||\]\[)
j=(j|_\|)
k=(k|\|<|/<|\\<|\|\{)
l=(l|1|\||\|_)
m=(m|\|\\/\||/\\/\\|\|'\|'\||\(\\/\)|/\\\\|/\|\\|/v\\)
n=(n|\|\\\||/\\/|\|\\\\\||/\|/)
o=(o|0|\(\)|\[\]|\{\})
p=(p|\|2|\|D)
q=(q|\(,\)|kw)
r=(r|\|2|\|Z|\|?)
s=[s$5]
t=(t|+|'\]\['|7)
u=(u|\|_\|)
v=(v|\|/|\\\||\\/|/)
w=(w|\\/\\/|\\\|\\\||\|/\|/|\\\|/|\\^/|//)
x=(x|><|\}\{)
y=(y|'/|`/|j)
z=(z|2|\(\\\))
A=(A|/-\\|4|@)
B=(B|\|3|\|o|8)
C=[C\(<KS]
D=(D|\|\)|o\||\|>|<\|)
E=[E3]
F=(F|Ph|PH|\|=)
G=[G\(69]
H=(H|\|\-\||\]\-\[|\}-\{|\(-\)|\)-\(|\}\{|#)
I=(I|1|!|\||\]\[)
J=(J|_\|)
K=(K|\|<|/<|\\<|\|\{)
L=(L|1|\||\|_)
M=(M|\|\\/\||/\\/\\|\|'\|'\||\(\\/\)|/\\\\|/\|\\|/v\\)
N=(N|\|\\\||/\\/|\|\\\\\||/\|/)
O=(O|0|\(\)|\[\]|\{\})
P=(P|\|2|\|D)
Q=(Q|\(,\)|kw)
R=(R|\|2|\|Z|\|?)
S=[S$5]
T=(T|+|'\]\['|7)
U=(U|\|_\|)
v=(V|\|/|\\\||\\/|/)
W=(W|\\/\\/|\\\|\\\||\|/\|/|\\\|/|\\^/|//)
X=(X|><|\}\{)
Y=(Y|'/|`/|j)
Z=(Z|2|\(\\\))

[List.Rexgen.Alpha:leet3_case]
a=(a|A|/-\\|4|@)
b=(b|B|\|3|\|o|8)
c=[cC\(<KS]
d=(d|D|\|\)|o\||\|>|<\|)
e=[eE3]
f=(f|F|ph|Ph|PH|\|=)
g=[gG\(69]
h=(h|H|\|\-\||\]\-\[|\}-\{|\(-\)|\)-\(|\}\{|#)
i=(i|I|1|!|\||\]\[)
j=(j|J|_\|)
k=(k|K|\|<|/<|\\<|\|\{)
l=(l|L|1|\||\|_)
m=(m|M|\|\\/\||/\\/\\|\|'\|'\||\(\\/\)|/\\\\|/\|\\|/v\\)
n=(n|N|\|\\\||/\\/|\|\\\\\||/\|/)
o=(o|O|0|\(\)|\[\]|\{\})
p=(p|P|\|2|\|D)
q=(q|Q|\(,\)|kw)
r=(r|R|\|2|\|Z|\|?)
s=[sS$5]
t=(t|T|+|'\]\['|7)
u=(u|U|\|_\|)
v=(v|V|\|/|\\\||\\/|/)
w=(w|W|\\/\\/|\\\|\\\||\|/\|/|\\\|/|\\^/|//)
x=(x|X|><|\}\{)
y=(y|Y|'/|`/|j)
z=(z|Z|2|\(\\\))
A=(a|A|/-\\|4|@)
B=(b|B|\|3|\|o|8)
C=[cC\(<KS]
D=(d|D|\|\)|o\||\|>|<\|)
E=[eE3]
F=(f|F|PH|Ph|ph|\|=)
G=[gG\(69]
H=(h|H|\|\-\||\]\-\[|\}-\{|\(-\)|\)-\(|\}\{|#)
I=(i|I|1|!|\||\]\[)
J=(j|J|_\|)
K=(k|K|\|<|/<|\\<|\|\{)
L=(l|L|1|\||\|_)
M=(m|M|\|\\/\||/\\/\\|\|'\|'\||\(\\/\)|/\\\\|/\|\\|/v\\)
N=(n|N|\|\\\||/\\/|\|\\\\\||/\|/)
O=(o|O|0|\(\)|\[\]|\{\})
P=(p|P|\|2|\|D)
Q=(q|Q|\(,\)|kw)
R=(r|R|\|2|\|Z|\|?)
S=[sS$5]
T=(t|T|+|'\]\['|7)
U=(u|U|\|_\|)
v=(v|V|\|/|\\\||\\/|/)
W=(w|W|\\/\\/|\\\|\\\||\|/\|/|\\\|/|\\^/|//)
X=(x|X|><|\}\{)
Y=(y|Y|'/|`/|j)
Z=(z|Z|2|\(\\\))

[List.Rexgen.Alpha:ascii2nonascii]
A=[ÀÁÂÃÄÅÆĀĂĄǍǞǠǺȀȂȦȺA]
B=[ƁƂɃʙB]
C=[ÇĆĈĊČƇȻC]
D=[ÐĎĐƉƊƋǱǲD]
E=[ÈÉÊËĒĔĖĘĚƎƏƐȄȆȨɆE]
F=[ƑF]
G=[ĜĞĠĢƓǤǦǴɢG]
H=[ĤĦǶȞʜH]
I=[ÌÍÎÏĨĪĬĮİƗǏȈȊɪI]
J=[ĴƖɈJ]
K=[ĶĸƘǨK]
L=[£ĹĻĽĿŁȽʟL]
M=[ƜM]
N=[ÑŃŅŇŊƝǸȠɴN]
O=[ÒÓÔÕÖØŌŎŐŒƆƟƠǑǪǬǾȌȎȪȬȮȰƢO]
P=[ƤP]
Q=[ɊQ]
R=[®ŔŖŘƦȐȒɌʀʁʶR]
S=[ŚŜŞŠƧȘS]
T=[ŢŤƬƮȚȾT]
U=[ÙÚÛÜŨŪŬŮŰŲƯǓǕǗǙǛȔȖɄU]
V=[ɅV]
W=[ŴW]
Y=[¥ÝŶŸƳȲɎʏY]
Z=[ŹŻŽƵȤZ]
a=[àáâãäåæāăąǎǟǡǻȁȃȧɐɑɒa]
b=[ƀƃɓb]
c=[¢©çćĉċčƈȼɕc]
d=[ðďđƌƍȡɖɗǳd]
e=[èéêëēĕėęěǝȅȇȩɇɘəɚɛɜɝɞe]
f=[ƒf]
g=[ĝğġģǥǧǵɠɡg]
h=[ħȟɥɦʮʯʰʱĥh]
i=[ìíîïĩīĭįıǐȉȋɨi]
j=[ĵǰȷɉɟʄʝʲj]
k=[ķƙǩʞ]
l=[ĺļľŀłƚƛȴɫɬɭl]
m=[µɯɰɱm]
n=[ñńņňŉŋƞǹȵɲɳn]
o=[òóôõöøōŏőœơǒǫǭǿȍȏȫȭȯȱɵƣȣo]
p=[ƥp]
q=[ɋʠq]
r=[ŕŗřȑȓɍɹɺɻɼɽɾɿʳʴʵr]
s=[śŝşšſƨșȿʂs]
t=[ţťŦŧƫƭțȶʇʈt]
u=[ùúûüũūŭůűųưǔǖǘǚǜȕȗʉu]
v=[ʌv]
w=[ŵʍʷw]
x=[×x]
y=[ýÿŷƴȳɏʎʸy]
z=[źżžƶȥɀʐʑz]
